<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PDF;

use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;
use Maatwebsite\Excel\Facades\Excel;

use App\Requests\InvoiceGenerationRequest;

use App\Http\Helpers\S3Helper;
use App\Http\Helpers\JsonExport;

class PDFGeneratorController extends Controller
{
    public function generatePDF(InvoiceGenerationRequest $request)
    {
        // dd($request->all());
        switch ($request->template) {
            case 'newInvoiceTemplate':
                $pdf = PDF::loadView('society.' . $request->template, [$request->data]);
                break;

            case 'oldInvoiceTemplate':
                $pdf = PDF::loadView('society.' . $request->template, [$request->data]);
                break;

            case 'receiptTemplate':
                $pdf = PDF::loadView('society.' . $request->template, ["data" => $request->data]);
                break;

            case 'incidentalBillTemplate':
                $pdf = PDF::loadView('.' . $request->template, ["data" => $request->data]);
                break;

            case 'purchaseOrderTemplate':
                $pdf = PDF::loadView('society.' . $request->template, ["data" => $request->data]);
                break;

            case 'paymentVoucherTemplate':
                // dd($request->data);
                $pdf = PDF::loadView('society.' . $request->template, ["data" => $request->data]);
                break;
            default:
                $pdf = PDF::loadView('email.oneSociety-invoice', []);
                break;
        }

        $s3Response = S3Helper::uploadFileToS3($pdf, $request->all());
        $s3Response = $s3Response->getData();

        if ($s3Response->status == 200) {
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        } else {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
    }

    public function generateExcel(Request $request)
    {
        $data = $request->data;
        $filename = $request->filename;
        $headings = $request->headings;

        $data = Excel::download(new JsonExport($data, $headings), $filename . '.xlsx');
        //get the file from Excel::download and pass it to S3Helper

        $file = $data->getFile();

        $s3Response = S3Helper::uploadExcelFileToS3($file, $request->all());
        $s3Response = $s3Response->getData();

        // echo "<pre>"; print_r($s3Response); die;
        if ($s3Response->status == 200) {
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        } else {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
    }

    public function mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2)
    {
        // Combine headings with a blank column
        $mergedHeadings = array_merge($headings1, [''], $headings2);

        // Combine rows of data with a blank column in between
        $maxRows = max(count($data1), count($data2));
        $mergedData = [];

        for ($i = 0; $i < $maxRows; $i++) {
            $row1 = $i < count($data1) ? array_values($data1[$i]) : array_fill(0, count($headings1), '');
            $row2 = $i < count($data2) ? array_values($data2[$i]) : array_fill(0, count($headings2), '');

            $mergedData[] = array_merge($row1, [''], $row2);
        }

        return ['headings' => $mergedHeadings, 'data' => $mergedData];
    }

    public function generateDoubleExcel(Request $request)
    {
        try {
            $data = $request->data;

            // Extract headings and data
            $headings1 = $data['heading1'];
            $data1 = $data['data1'];
            $headings2 = $data['heading2'];
            $data2 = $data['data2'];
            $filename = $request->filename;

            // Merge tables with blank column
            $mergedTable = $this->mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2);

            // echo "<pre>"; print_r($mergedTable); die;
            // Validate headings count
            $headingsCount = count($mergedTable['headings']);
            if ($headingsCount <= 0) {
                throw new \Exception('Invalid headings count. Cannot generate Excel.');
            }

            // Ensure all rows have the correct number of columns
            foreach ($mergedTable['data'] as $rowIndex => $row) {
                if (count($row) != $headingsCount) {
                    throw new \Exception("Row $rowIndex does not match the headings count.");
                }
            }

            // Generate merge range for headings
            $mergeRange = $this->generateMergeRange($headingsCount);

            // Create and customize Excel export
            $export = new JsonExport($mergedTable['data'], $mergedTable['headings']);
            $data = Excel::download($export, $filename . '.xlsx');

            // Merge cells for headings
            $export->sheet(function ($sheet) use ($mergeRange) {
                $sheet->getDelegate()->mergeCells($mergeRange);
            });

            // Upload to S3
            $file = $data->getFile();
            $s3Response = S3Helper::uploadExcelFileToS3($file, $request->all());
            $s3Response = $s3Response->getData();

            // Handle S3 response
            if ($s3Response->status == 200) {
                return response()->json([
                    'data' => $s3Response->data,
                    'message' => 'success',
                    'status' => 200
                ]);
            } else {
                return response()->json([
                    'message' => 'There was an error uploading the file.',
                    'error' => $s3Response->error,
                    'status' => 500
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An unexpected error occurred while generating the Excel file.',
                'error' => $e->getMessage(),
                'status' => 500
            ]);
        }
    }

    public function downloadPDF(Request $request)
    {
        ini_set('memory_limit', '2048M');
        ini_set('max_execution_time', 300);
        $data = $request->data;
        $headings = $request->headings;
        $header = $request->header;
        $summaryHeadings = $request->summaryHeadings;
        $company_details = $request->company_details;
        $company_details['society_name']=$request->society_name;
        $filename = $request->file_name ? str_replace(' ', '_', $request->file_name) : 'downloadPDF';
        $timestamp = date(format: 'Y-m-d H:i:s');
        $filename = $filename.''.$timestamp;


        // Split filename at the start of a number
        $parts = preg_split('/(?<=\D)(?=\d)/', $filename, 2);
        $filename = $parts[0];
        $pdf = PDF::loadView('society.' . $filename, [
            "data" => $data,
            "headings" => $headings,
            "header" => $header,
            "summaryHeadings" => $summaryHeadings,
            "company_details" => $company_details
        ]);



        $s3Response = S3Helper::uploadFileToS3($pdf, $request->all());
        $s3Response = $s3Response->getData();
        //dd($s3Response);
        if ($s3Response->status == 200) {
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        } else {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
    }

    public function downloadNoc(Request $request)
    {
        $data = $request->data;

        $pdf = PDF::loadView('society.downloadNoc', ["data" => $data])->setPaper('a4', 'landscape');

        $s3Response = S3Helper::uploadFileToS3($pdf, $request->all());
        $s3Response = $s3Response->getData();

        if ($s3Response->status == 200) {
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        } else {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
    }

    public function downloadNotice(Request $request)
    {
        $data = $request->data;

        $pdf = PDF::loadView('society.notice', ["data" => $data]);

        $s3Response = S3Helper::uploadFileToS3($pdf, $request->all());
        $s3Response = $s3Response->getData();

        if ($s3Response->status == 200) {
            return response()->json([
                'data' => $s3Response->data,
                'message' => 'success',
                'status' => 200
            ]);
        } else {
            return response()->json([
                'message' => 'There was an error uploading the file.',
                'error' => $s3Response->error,
                'status' => 500
            ]);
        }
    }
}
