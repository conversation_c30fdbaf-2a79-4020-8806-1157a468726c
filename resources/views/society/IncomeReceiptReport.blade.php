<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Income Receipt Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 9px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 8px; }
        .receipt-date-col { width: 10%; text-align: center; }
        .receipt-no-col { width: 12%; text-align: center; }
        .receipt-type-col { width: 12%; text-align: center; }
        .paid-by-col { width: 18%; text-align: left; }
        .invoice-no-col { width: 12%; text-align: center; }
        .payment-ref-col { width: 12%; text-align: center; }
        .unit-col { width: 12%; text-align: center; }
        .amount-col { width: 12%; text-align: right; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; width: 100%; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; text-align: right; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Income Receipt Report' ?? 'Society Report' }}</p>
    </div>



    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data) && count($data) >= 2) {
                $reportData = $data[0] ?? [];
                $summaryData = $data[1][0] ?? [];
            }
        @endphp

        @if(!empty($reportData))
            
            <table style="width:100%;">
    <thead style="display: table-header-group;">
        <tr>
            <th colspan="8" style="text-align:center; padding: 10px;">
                <div style="margin-bottom:0;margin-top:5px;font-size:1.4em;font-weight:bold;">{{$company_details['society_name'] ?? 'BHAKTI PARK PHASE-II BLDG NO.1 CHS LTD'}}</div>
                <div style="margin-bottom:0;margin-top:5px;">Reg No: {{$company_details['society_reg_num'] ?? 'TNA/(TNA)/HSG/(TC)22641/2011'}} Dated {{$company_details['registration_date'] ?? '04/01/2011'}}</div>
                <div style="margin-bottom:0;margin-top:5px;">{{$company_details['society_address'] ?? ''}}</div>
            </th>
        </tr>
        <tr>
            <th class="small-font receipt-date-col">Receipt Date</th>
            <th class="small-font receipt-no-col">Receipt No</th>
            <th class="small-font receipt-type-col">Receipt Type</th>
            <th class="small-font paid-by-col">Paid By</th>
            <th class="small-font invoice-no-col">Invoice No</th>
            <th class="small-font payment-ref-col">Payment Reference</th>
            <th class="small-font unit-col">Building / Unit</th>
            <th class="small-font amount-col">Paid Amount</th>
        </tr>
    </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font receipt-date-col">{{ date('d/m/Y', strtotime($row['receipt_date'] ?? '')) }}</td>
                            <td class="small-font receipt-no-col">{{ $row['receipt_number'] ?? '' }}</td>
                            <td class="small-font receipt-type-col">{{ $row['receipt_type'] ?? '' }}</td>
                            <td class="small-font paid-by-col">{{ $row['paid_by'] ?? '' }}</td>
                            <td class="small-font invoice-no-col">{{ $row['invoice_number'] ?? '' }}</td>
                            <td class="small-font payment-ref-col">{{ $row['payment_reference'] ?? '' }}</td>
                            <td class="small-font unit-col">{{ $row['unit'] ?? '' }}</td>
                            <td class="small-font amount-col">{{ number_format($row['payment_amount'] ?? 0, 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Summary Section --}}
            @if(!empty($summaryData))
                <div class="summary-section">
                    <div class="summary-title">Summary</div>
                    <table class="summary-table">
                        <tbody>
                            <tr>
                                <td style="width: 88%; text-align: right; font-weight: bold; font-size: 12px;">Total</td>
                                <td style="width: 12%; text-align: right; font-weight: bold; font-size: 12px;">{{ number_format($summaryData['total_amount'] ?? 0, 2) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            @endif

        @else
            <div class="no-records">
                <p>No income receipt data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
