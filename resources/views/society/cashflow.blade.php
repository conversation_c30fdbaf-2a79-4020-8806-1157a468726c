<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Flow Statement</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 12px;
            line-height: 1.2;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }
        
        .reg-number {
            font-size: 10px;
            margin: 2px 0;
        }
        
        .address {
            font-size: 10px;
            margin: 2px 0;
        }
        
        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }
        
        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 11px;
        }
        
        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
        }
        
        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 6px 4px;
        }
        
        .inflow-header {
            background-color: #e6e6e6;
            font-weight: bold;
            text-align: center;
        }
        
        .outflow-header {
            background-color: #e6e6e6;
            font-weight: bold;
            text-align: center;
        }
        
        .amount-cell {
            text-align: right;
            padding-right: 8px;
        }
        
        .account-cell {
            text-align: left;
            padding-left: 8px;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        
        .net-inflow-row {
            font-weight: bold;
            background-color: #e6e6e6;
        }
        
        .report-date {
            margin-top: 10px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</div>
        <div class="reg-number">[REGN.NO:{{$company_details['society_reg_num'] ?? 'mumbau'}}]</div>
        <div class="address">{{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}</div>
        <div class="report-title">Cash Flow for {{$company_details['year'] ?? '2025-2026'}}</div>
    </div>

    <!-- Cash Flow Table -->
    <table class="table-container">
        <thead>
            <tr>
                <th style="width: 40%;" class="inflow-header">Inflow</th>
                <th style="width: 10%;" class="inflow-header">Amount</th>
                <th style="width: 40%;" class="outflow-header">Outflow</th>
                <th style="width: 10%;" class="outflow-header">Amount</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalInflow = 0;
                $totalOutflow = 0;
            @endphp
            
            @forelse ($data as $row)
                @php
                    $inflowAmount = is_numeric($row['amount']) ? (float)$row['amount'] : 0;
                    $outflowAmount = is_numeric($row['amount1']) ? (float)$row['amount1'] : 0;
                    
                    $totalInflow += $inflowAmount;
                    $totalOutflow += $outflowAmount;
                @endphp
                <tr>
                    <td class="account-cell">{{ $row['ledger_account_name'] ?? '' }}</td>
                    <td class="amount-cell">
                        @if($inflowAmount != 0)
                            {{ number_format(abs($inflowAmount), 2) }}
                        @endif
                    </td>
                    <td class="account-cell">{{ $row['ledger_account_name1'] ?? '' }}</td>
                    <td class="amount-cell">
                        @if($outflowAmount != 0)
                            {{ number_format(abs($outflowAmount), 2) }}
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" style="text-align: center;">No data available</td>
                </tr>
            @endforelse
            
            {{-- Total Row --}}
            @if(!empty($data))
                <tr class="total-row">
                    <td class="account-cell">Total</td>
                    <td class="amount-cell">{{ number_format($totalInflow, 2) }}</td>
                    <td class="account-cell">Total</td>
                    <td class="amount-cell">{{ number_format($totalOutflow, 2) }}</td>
                </tr>
                
                {{-- Net Inflow Row --}}
                <tr class="net-inflow-row">
                    <td class="account-cell">Net Inflow</td>
                    <td class="amount-cell"></td>
                    <td class="account-cell"></td>
                    <td class="amount-cell">{{ number_format($totalInflow - $totalOutflow, 2) }}</td>
                </tr>
            @endif
        </tbody>
    </table>
    
    <div class="report-date">
        Report Date: {{ date('jS F Y') }}
    </div>
</body>
</html>
