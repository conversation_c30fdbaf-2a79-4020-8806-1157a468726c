<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trial Balance</title>
    <style>
        @page {
            margin: 120px 20px 60px 20px; /* top, right, bottom, left */
            @top-center {
                content: element(page-header);
            }
            @bottom-center {
                content: element(page-footer);
            }
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .page-header {
            position: running(page-header);
            text-align: center;
            font-size: 12px;
            line-height: 1.3;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 10px;
        }

        .page-footer {
            position: running(page-footer);
            text-align: center;
            font-size: 10px;
            padding: 5px 0;
            border-top: 1px solid #ccc;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2, .header p, .header h3 {
            margin: 5px 0;
        }
        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 12px;
        }
        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 4px;
            font-size: 11px;
            vertical-align: top;
        }
        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 6px 4px;
        }
        .table-container tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* Special styling for category rows */
        .category-row {
            font-weight: bold;
            background-color: #e6e6e6 !important;
        }

        /* Indent sub-items */
        .sub-item {
            padding-left: 15px !important;
        }
        .report-footer {
            margin-top: 20px;
            text-align: right;
            font-size: 14px;
        }
        .report-footer p {
            margin: 0;
        }

        /* Ensure table headers repeat on each page */
        .table-container thead {
            display: table-header-group;
        }

        /* Avoid page breaks inside table rows */
        .table-container tr {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <!-- Page Header - Will repeat on every page -->
    <div class="page-header">
        <h3 style="margin: 0; font-size: 14px;">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</h3>
        <div style="font-size: 10px; margin: 2px 0;">
            [REGN.NO:{{$company_details['society_reg_num'] ?? 'mumbau'}}]
        </div>
        <div style="font-size: 10px;">
            {{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}
        </div>
        <div style="font-size: 12px; font-weight: bold; margin-top: 5px;">
            Trial Balance for {{$company_details['year'] ?? '2024-2025'}}
        </div>
    </div>

    <!-- Page Footer - Will repeat on every page -->
    <div class="page-footer">
        <div>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }} | Page <span class="page-number"></span></div>
    </div>

    <!-- Main Content starts directly with table -->

    <table class="table-container">
        <thead>
            <tr>
                <th style="width: 25%; text-align: left;">Particular</th>
                <th style="width: 12%; text-align: right;">Opening(Debit)</th>
                <th style="width: 12%; text-align: right;">Closing(Debit)</th>
                <th style="width: 25%; text-align: left;">Particular</th>
                <th style="width: 13%; text-align: right;">Opening(Credit)</th>
                <th style="width: 13%; text-align: right;">Closing(Credit)</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalOpeningDebit = 0;
                $totalClosingDebit = 0;
                $totalOpeningCredit = 0;
                $totalClosingCredit = 0;
            @endphp

            @forelse ($data as $row)
                @php
                    $openingDebit = is_numeric($row['opening_debit']) ? (float)$row['opening_debit'] : 0;
                    $closingDebit = is_numeric($row['closing_debit']) ? (float)$row['closing_debit'] : 0;
                    $openingCredit = is_numeric($row['opening_credit']) ? (float)$row['opening_credit'] : 0;
                    $closingCredit = is_numeric($row['closing_credit']) ? (float)$row['closing_credit'] : 0;

                    // Only add positive values to totals
                    if ($openingDebit > 0) $totalOpeningDebit += $openingDebit;
                    if ($closingDebit > 0) $totalClosingDebit += $closingDebit;
                    if ($openingCredit > 0) $totalOpeningCredit += $openingCredit;
                    if ($closingCredit > 0) $totalClosingCredit += $closingCredit;
                @endphp
                <tr>
                    <td style="text-align: left; padding-left: 5px;">{{ $row['debit_particular'] ?? '' }}</td>
                    <td style="text-align: right; padding-right: 5px;">
                        @if($openingDebit != 0)
                            {{ number_format(abs($openingDebit), 2) }}
                        @endif
                    </td>
                    <td style="text-align: right; padding-right: 5px;">
                        @if($closingDebit != 0)
                            {{ number_format(abs($closingDebit), 2) }}
                        @endif
                    </td>
                    <td style="text-align: left; padding-left: 5px;">{{ $row['credit_particular'] ?? '' }}</td>
                    <td style="text-align: right; padding-right: 5px;">
                        @if($openingCredit != 0)
                            {{ number_format(abs($openingCredit), 2) }}
                        @endif
                    </td>
                    <td style="text-align: right; padding-right: 5px;">
                        @if($closingCredit != 0)
                            {{ number_format(abs($closingCredit), 2) }}
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="text-align: center;">No data available</td>
                </tr>
            @endforelse

            {{-- Total Row --}}
            @if(!empty($data))
                <tr style="border-top: 2px solid #000; font-weight: bold;">
                    <td style="text-align: left; padding-left: 5px;">Total</td>
                    <td style="text-align: right; padding-right: 5px;">{{ number_format($totalOpeningDebit, 2) }}</td>
                    <td style="text-align: right; padding-right: 5px;">{{ number_format($totalClosingDebit, 2) }}</td>
                    <td style="text-align: left; padding-left: 5px;">Total</td>
                    <td style="text-align: right; padding-right: 5px;">{{ number_format($totalOpeningCredit, 2) }}</td>
                    <td style="text-align: right; padding-right: 5px;">{{ number_format($totalClosingCredit, 2) }}</td>
                </tr>
            @endif
        </tbody>
    </table>

    <div class="report-footer">
        <p>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }}</p>
    </div>
</body>
</html>
