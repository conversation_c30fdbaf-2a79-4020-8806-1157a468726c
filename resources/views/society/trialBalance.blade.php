<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trial Balance</title>
    <style>
        @page {
            margin: 120px 20px 60px 20px; /* top, right, bottom, left */
            @top-center {
                content: element(page-header);
            }
            @bottom-center {
                content: element(page-footer);
            }
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .page-header {
            position: running(page-header);
            text-align: center;
            font-size: 12px;
            line-height: 1.3;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 10px;
        }

        .page-footer {
            position: running(page-footer);
            text-align: center;
            font-size: 10px;
            padding: 5px 0;
            border-top: 1px solid #ccc;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2, .header p, .header h3 {
            margin: 5px 0;
        }
        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
        }
        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 14px;
        }
        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .table-container tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .report-footer {
            margin-top: 20px;
            text-align: right;
            font-size: 14px;
        }
        .report-footer p {
            margin: 0;
        }

        /* Ensure table headers repeat on each page */
        .table-container thead {
            display: table-header-group;
        }

        /* Avoid page breaks inside table rows */
        .table-container tr {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <!-- Page Header - Will repeat on every page -->
    <div class="page-header">
        <h3 style="margin: 0; font-size: 14px;">{{$company_details['soc_name'] ?? 'Society Name'}}</h3>
        <div style="font-size: 10px; margin: 2px 0;">
            Reg No: {{$company_details['soc_reg_num'] ?? 'Registration Number'}}
            @isset($company_details['soc_gst_number'])
             | GSTIN/UIN: {{$company_details['soc_gst_number']}}
            @endisset
        </div>
        <div style="font-size: 10px;">
            {{$company_details['soc_address_1'] ?? ''}}, {{$company_details['soc_city_or_town'] ?? ''}}, {{$company_details['soc_state'] ?? ''}} {{$company_details['soc_pincode'] ?? ''}}
        </div>
        <div style="font-size: 12px; font-weight: bold; margin-top: 5px;">
            Trial Balance
            @isset($company_details['year'])
            for {{$company_details['year']}}
            @endisset
        </div>
    </div>

    <!-- Page Footer - Will repeat on every page -->
    <div class="page-footer">
        <div>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }} | Page <span class="page-number"></span></div>
    </div>

    <!-- Main Content Header (First Page Only) -->
    <div style="width:100%;text-align:center;margin-top:2px;">
        <h2 style="margin-bottom: 0;margin-top: 0px;">{{$company_details['soc_name']}}</h2>
        <div style="width:100%;text-align:center;line-height:1.5;color:#000000;margin-top:5px;margin-bottom:15px;margin-right:0px;margin-left:0px;font-size: 11px;">
            Reg No: {{$company_details['soc_reg_num']}}
            @isset($company_details['soc_gst_number'])
             | GSTIN/UIN: {{$company_details['soc_gst_number']}}
            @endisset
             <br/>
            {{$company_details['soc_address_1']}}, {{$company_details['soc_address_2']}}, {{$company_details['soc_landmark']}}, {{$company_details['soc_city_or_town']}}, {{$company_details['soc_state']}}, {{$company_details['soc_pincode']}}.<br>
            @isset($company_details['soc_office_email'])
            Email: {{$company_details['soc_office_email']}}
            @endisset
            @isset($company_details['soc_office_mobile'])
            | Phone: {{$company_details['soc_office_mobile']}}
            @endisset
        </div>
        @isset($company_details['year'])
             Trial Balance for {{$company_details['year']}}
        @endisset
    </div>

    <table class="table-container">
        <thead>
            <tr>
                <th>Particular (Debit)</th>
                <th>Opening (Debit)</th>
                <th>Closing (Debit)</th>
                <th>Particular (Credit)</th>
                <th>Opening (Credit)</th>
                <th>Closing (Credit)</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($data as $row)
                <tr>
                    <td>{{ $row['debit_particular'] ?? '-' }}</td>
                    <td>{{ number_format($row['opening_debit'] ?? 0, 2) }}</td>
                    <td>{{ number_format($row['closing_debit'] ?? 0, 2) }}</td>
                    <td>{{ $row['credit_particular'] ?? '-' }}</td>
                    <td>{{ number_format($row['opening_credit'] ?? 0, 2) }}</td>
                    <td>{{ number_format($row['closing_credit'] ?? 0, 2) }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">No data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="report-footer">
        <p>Report Date: {{ $reportDate ?? now()->format('d-m-Y') }}</p>
    </div>
</body>
</html>
