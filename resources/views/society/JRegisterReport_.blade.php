<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'J Register Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #000; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .form-title { text-align: center; font-size: 1.4em; font-weight: bold; margin-bottom: 20px; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .serial-col { width: 10%; text-align: center; }
        .name-col { width: 40%; text-align: left; }
        .address-col { width: 35%; text-align: left; }
        .class-col { width: 15%; text-align: center; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'J Register Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="form-title">FORM-J</div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            } elseif (isset($data) && is_object($data) && method_exists($data, 'toArray')) {
                // Handle Laravel Collection
                $reportData = $data->toArray();
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font serial-col">Sr No</th>
                        <th class="small-font name-col">Full Name of the Member</th>
                        <th class="small-font address-col">Address</th>
                        <th class="small-font class-col">Class of Member</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $index => $row)
                        <tr>
                            <td class="small-font serial-col">{{ $index + 1 }}</td>
                            <td class="small-font name-col">{{ strtoupper($row['full_name'] ?? '') }}</td>
                            <td class="small-font address-col">
                                @if(!empty($row['address']))
                                    {{ $row['address'] }}
                                @else
                                    &nbsp;
                                @endif
                            </td>
                            <td class="small-font class-col">{{ $row['member_type_name'] ?? '' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="no-records">
                <p>No member data available for the J Register.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
