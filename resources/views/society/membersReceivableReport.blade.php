<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Members Receivable' ?? 'Society Report' }}</p>
    </div>
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];
            // Check if data exists and is properly structured
            if (isset($data) && is_array($data) && count($data) >= 1) {
                $reportData = $data[0] ?? [];
                $summaryData = $data[1] ?? [];
            }
            // Get headers from first non-empty row or use default headers
            $headers = ['Building / Unit', 'Primary Member', 'Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance'];
            if (!empty($reportData)) {
                $firstRow = reset($reportData);
                if (is_array($firstRow) && !empty($firstRow)) {
                    $headers = array_keys($firstRow);
                }
            }
        @endphp
        @if(!empty($reportData))
            <table style="width:100%;">
                <thead style="display: table-header-group;">
                    <tr>
                        <th colspan="{{ count($headers) }}" style="text-align:center; padding: 10px;">
                            <div style="margin-bottom:0;margin-top:5px;font-size:1.4em;font-weight:bold;">{{$company_details['society_name'] ?? 'River Walk CHS'}}</div>
                            <div style="margin-bottom:0;margin-top:5px;">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</div>
                            <div style="margin-bottom:0;margin-top:5px;">{{$company_details['society_address'] ?? 'Society Address'}}</div>
                            @if(isset($report_title))
                                <div class="sub-title" style="margin:0;">{{ $report_title }}</div>
                            @endif
                            @if(isset($date_range))
                                <div class="text-center" style="margin:0;">
                                    <strong>Date: {{ $date_range }}</strong>
                                </div>
                            @endif
                        </th>
                    </tr>
                    <tr>
                        @foreach($headers as $header)
                            <th class="text-center">{{ $header }}</th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        @php
                            // Skip the total row in main data as it will be handled separately
                            $isTotalRow = isset($row['Building / Unit']) && $row['Building / Unit'] === 'Total';
                        @endphp
                        @if(!$isTotalRow)
                            <tr>
                                @foreach($headers as $header)
                                    <td class="{{ in_array($header, ['Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance']) ? 'text-right' : '' }}">
                                        @php
                                            $value = $row[$header] ?? '';
                                            // Handle blank/empty values for display
                                            if ($value === '' || $value === null) {
                                                if (in_array($header, ['Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance'])) {
                                                    $value = 0; // Show 0 for monetary columns when blank
                                                } else {
                                                    $value = ''; // Keep blank for text columns
                                                }
                                            }
                                        @endphp
                                        @if(in_array($header, ['Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance']))
                                            {{ number_format($value, 2) }}
                                        @else
                                            {{ $value }}
                                        @endif
                                    </td>
                                @endforeach
                            </tr>
                        @endif
                    @endforeach

                    {{-- Total Row --}}
                    @if(!empty($summaryData))
                        <tr class="total-row">
                            @foreach($headers as $header)
                                <td class="{{ in_array($header, ['Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance']) ? 'text-right' : 'text-center' }}" style="font-weight: bold;">
                                    @if($header === 'Building / Unit')
                                        Total
                                    @elseif(in_array($header, ['Maintenance Dues', 'Incidental Dues', 'Credit Balance', 'Ledger Balance']))
                                        @php
                                            $summaryValue = $summaryData[$header] ?? 0;
                                        @endphp
                                        {{ number_format($summaryValue, 2) }}
                                    @else
                                        {{ $summaryData[$header] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endif
                </tbody>
            </table>

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No members receivable data available for the selected criteria.</p>
            </div>
        @endif
    </div>
    
    {{-- Summary Section --}}
    @if(!empty($summaryData))
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        <th class="text-center">Maintenance Dues</th>
                        <th class="text-center">Incidental Dues</th>
                        <th class="text-center">Credit Balance</th>
                        <th class="text-center">Ledger Balance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">{{ number_format($summaryData['Maintenance Dues'] ?? 0, 2) }}</td>
                        <td class="text-center">{{ number_format($summaryData['Incidental Dues'] ?? 0, 2) }}</td>
                        <td class="text-center">{{ number_format($summaryData['Credit Balance'] ?? 0, 2) }}</td>
                        <td class="text-center">{{ number_format($summaryData['Ledger Balance'] ?? 0, 2) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
