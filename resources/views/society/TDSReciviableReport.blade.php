<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .sum-row { background-color: #f0f0f0; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .unit-col { width: 20%; text-align: left; }
        .member-col { width: 30%; text-align: left; }
        .invoice-col { width: 20%; text-align: center; }
        .payment-col { width: 15%; text-align: right; }
        .tds-col { width: 15%; text-align: right; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'TDS Receivable Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">TDS Receivable Report</div>
    <div class="filter-info">Date: {{ $date_from ?? '01/07/2025' }} To {{ $date_to ?? '31/07/2025' }} , Filter By: Transaction Date</div>
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data) && count($data) >= 2) {
                $reportData = $data[0][0] ?? [];
                $summaryData = $data[1][0] ?? [];
            }
        @endphp

        @if(!empty($reportData))

            <table>
                <thead>
                    <tr>
                        <th class="small-font unit-col">Building / Unit</th>
                        <th class="small-font member-col">Primary Member / Non Member</th>
                        <th class="small-font invoice-col">Invoice Number</th>
                        <th class="small-font payment-col">Payment Amount</th>
                        <th class="small-font tds-col">TDS Deducted</th>
                    </tr>
                </thead>
                <tbody>
                    @if(is_array($reportData))
                        {{-- Single row data --}}
                        <tr>
                            <td class="small-font unit-col">{{ $reportData['unit'] ?? '' }}</td>
                            <td class="small-font member-col">{{ $reportData['member_name'] ?? '' }}</td>
                            <td class="small-font invoice-col">{{ $reportData['invoice_number'] ?? '' }}</td>
                            <td class="small-font payment-col">{{ number_format($reportData['payment_amount'] ?? 0, 2) }}</td>
                            <td class="small-font tds-col">{{ number_format($reportData['tds_deducted'] ?? 0, 2) }}</td>
                        </tr>
                    @else
                        {{-- Multiple rows data --}}
                        @foreach($reportData as $row)
                            <tr>
                                <td class="small-font unit-col">{{ $row['unit'] ?? '' }}</td>
                                <td class="small-font member-col">{{ $row['member_name'] ?? '' }}</td>
                                <td class="small-font invoice-col">{{ $row['invoice_number'] ?? '' }}</td>
                                <td class="small-font payment-col">{{ number_format($row['payment_amount'] ?? 0, 2) }}</td>
                                <td class="small-font tds-col">{{ number_format($row['tds_deducted'] ?? 0, 2) }}</td>
                            </tr>
                        @endforeach
                    @endif

                    {{-- SUM Row --}}
                    @if(!empty($summaryData))
                        <tr class="sum-row">
                            <td class="small-font" colspan="3" style="text-align: right; font-weight: bold;">SUM</td>
                            <td class="small-font payment-col" style="font-weight: bold;">{{ number_format($summaryData['total_payment_amount'] ?? 0, 2) }}</td>
                            <td class="small-font tds-col" style="font-weight: bold;">{{ number_format($summaryData['total_tds_deducted'] ?? 0, 2) }}</td>
                        </tr>
                    @endif
                </tbody>
            </table>

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No TDS receivable data available for the selected criteria.</p>
            </div>
        @endif
    </div>
    {{-- Summary Table --}}
    @if(!empty($summaryData))
        <div style="margin-top: 30px;">
            <h3 style="margin-bottom: 10px;">Summary</h3>
            <table style="width: 50%; width:100%;margin: 0;">
                <thead>
                    <tr>
                        <th class="small-font text-center" style="width: 50%;">Payment Amount</th>
                        <th class="small-font text-center" style="width: 50%;">TDS Deducted</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="small-font text-center">{{ number_format($summaryData['total_payment_amount'] ?? 0, 2) }}</td>
                        <td class="small-font text-center">{{ number_format($summaryData['total_tds_deducted'] ?? 0, 2) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    @endif

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
