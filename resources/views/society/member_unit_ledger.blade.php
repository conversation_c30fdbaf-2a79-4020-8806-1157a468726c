<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .unit-info { text-align: center; margin-bottom: 20px; font-weight: bold; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
        .opening-balance-row { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Member Unit Ledger Report' ?? 'Society Report' }}</p>
    </div>
    

    
    @if(isset($start_date) && isset($end_date))
    <div class="text-center">
        <strong>StartDate: {{ $start_date }} EndDate: {{ $end_date }}</strong>
    </div>
    @endif
    
    @if(isset($unit_id))
    <div class="unit-info">
        Unit_Id: {{ $unit_id }}
    </div>
    @endif
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1][0] ?? [];

                // Define custom order for columns to match the member unit ledger layout
                $orderedHeaders = [
                    'id',
                    'date',
                    'particulars',
                    'vch_type',
                    'vch_no',
                    'debit',
                    'credit'
                ];

                // Get dynamic headers from first row of data
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $allHeaders = array_keys($firstRow);
                        
                        // Filter and order headers based on what exists in data
                        foreach ($orderedHeaders as $orderedHeader) {
                            if (in_array($orderedHeader, $allHeaders)) {
                                $headers[] = $orderedHeader;
                            }
                        }
                        
                        // Add any remaining headers not in the ordered list
                        foreach ($allHeaders as $header) {
                            if (!in_array($header, $headers) && !in_array($header, ['created_at', 'updated_at'])) {
                                $headers[] = $header;
                            }
                        }
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table style="width:100%;">
    <thead style="display: table-header-group;">
        <tr>
            <th colspan="{{ count($headers) }}" style="text-align:center; padding: 10px;">
                <div style="margin-bottom:0;margin-top:5px;font-size:1.4em;font-weight:bold;">{{$company_details['society_name'] ?? 'River Walk CHS'}}</div>
                <div style="margin-bottom:0;margin-top:5px;">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</div>
                <div style="margin-bottom:0;margin-top:5px;">{{$company_details['society_address'] ?? 'Society Address'}}</div>
            </th>
        </tr>
        <tr>
            @foreach($headers as $header)
                <th class="small-font">
                    @switch($header)
                        @case('id')
                            ID
                            @break
                        @case('date')
                            Date
                            @break
                        @case('particulars')
                            Particulars
                            @break
                        @case('vch_type')
                            Vch Type
                            @break
                        @case('vch_no')
                            Vch No.
                            @break
                        @case('debit')
                            Debit
                            @break
                        @case('credit')
                            Credit
                            @break
                        @default
                            {{ ucwords(str_replace('_', ' ', $header)) }}
                    @endswitch
                </th>
            @endforeach
        </tr>
    </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                <td class="{{ in_array($header, ['debit', 'credit']) ? 'text-right small-font' : 'small-font' }}">
                                    @if(in_array($header, ['debit', 'credit']) && isset($row[$header]) && is_numeric($row[$header]))
                                        {{ number_format($row[$header], 2) }}
                                    @elseif($header === 'date' && isset($row[$header]))
                                        {{ date('Y-m-d', strtotime($row[$header])) }}
                                    @else
                                        {{ $row[$header] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
            


            {{-- Summary Section --}}
            @if(isset($totalsData) && !empty($totalsData))
            <div class="summary-section">
                <div class="summary-title text-center">Summary</div>
                <table class="summary-table">
                    <thead>
                        <tr>
                            @php
                                // totalsData is the summary object from $data[1][0]
                                $summaryData = $totalsData;

                                // Define summary headers mapping based on actual data keys from your structure
                                $summaryHeaders = [
                                    'debit_sum' => 'Total Debit',
                                    'credit_sum' => 'Total Credit',
                                    'balance' => 'Balance'
                                ];
                            @endphp

                            @foreach($summaryHeaders as $key => $label)
                                @if(isset($summaryData[$key]))
                                <th class="text-center">{{ $label }}</th>
                                @endif
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="total-row">
                            @foreach($summaryHeaders as $key => $label)
                                @if(isset($summaryData[$key]))
                                <td class="text-center">{{ number_format($summaryData[$key], 2) }}</td>
                                @endif
                            @endforeach
                        </tr>
                    </tbody>
                </table>
            </div>
            @endif
        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
            </div>
        @endif
    </div>
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
