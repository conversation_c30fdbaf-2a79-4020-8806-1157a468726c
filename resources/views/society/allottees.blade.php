<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Allottees Report</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
        }

        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }

        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }

        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }

        .allottees-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 10px;
        }

        .allottees-table th,
        .allottees-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            vertical-align: top;
        }

        .allottees-table th {
            background-color: #e8e8e8;
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }

        .unit-header {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 11px;
        }

        .member-row td {
            text-align: left;
            padding: 4px 6px;
        }

        .name-cell {
            width: 20%;
            text-align: left;
            padding-left: 8px;
        }

        .email-cell {
            width: 25%;
            text-align: left;
        }

        .mobile-cell {
            width: 15%;
            text-align: center;
        }

        .type-cell {
            width: 15%;
            text-align: center;
        }

        .signature-cell {
            width: 25%;
            text-align: center;
            height: 25px;
        }

        .report-date {
            margin-top: 15px;
            font-size: 9px;
            text-align: right;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT LTD'}}</div>
        
        <div class="report-title">Allottees Report</div>
    </div>

    <!-- Allottees Table -->
    <table class="allottees-table">
        <thead>
            <tr>
                <th class="name-cell">Name</th>
                <th class="email-cell">Email Address</th>
                <th class="mobile-cell">Mobile Number</th>
                <th class="type-cell">Member Type</th>
                <th class="signature-cell">Signature</th>
            </tr>
        </thead>
        <tbody>
            @forelse($data as $unit)
                {{-- Unit Header Row --}}
                <tr class="unit-header">
                    <td colspan="5">{{ $unit['building_unit'] ?? 'Unknown Unit' }}</td>
                </tr>

                {{-- Members for this unit --}}
                @if(isset($unit['rows']) && is_array($unit['rows']))
                    @foreach($unit['rows'] as $member)
                        <tr class="member-row">
                            <td class="name-cell">
                                @php
                                    $salute = !empty($member['salute']) ? $member['salute'] . ' ' : '';
                                    $firstName = $member['member_first_name'] ?? '';
                                    $lastName = $member['member_last_name'] ?? '';
                                    $fullName = trim($salute . $firstName . ' ' . $lastName);
                                @endphp
                                {{ $fullName }}
                            </td>
                            <td class="email-cell">{{ $member['member_email_id'] ?? '' }}</td>
                            <td class="mobile-cell">{{ $member['member_mobile_number'] ?? '' }}</td>
                            <td class="type-cell">{{ $member['member_type_name'] ?? '' }}</td>
                            <td class="signature-cell"></td>
                        </tr>
                    @endforeach
                @else
                    <tr class="member-row">
                        <td colspan="5" style="text-align: center; font-style: italic;">No members found for this unit</td>
                    </tr>
                @endif
            @empty
                <tr>
                    <td colspan="5" style="text-align: center; font-style: italic;">No data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="report-date">
        Report Generated: {{ date('jS F Y, g:i A') }}
    </div>
</body>
</html> 