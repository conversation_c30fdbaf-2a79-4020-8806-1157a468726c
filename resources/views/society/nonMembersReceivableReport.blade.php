<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Non Members Receivable Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .name-col { width: 40%; text-align: left; }
        .payment-dues-col { width: 20%; text-align: right; }
        .credit-balance-col { width: 20%; text-align: right; }
        .ledger-balance-col { width: 20%; text-align: right; }
        .total-row { background-color: #f0f0f0; font-weight: bold; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Non Members Receivable Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'mumbai'}} | GSTIN/UIN: {{$company_details['gstin'] ?? 'GSTIN002382894'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Nonmembers Receivable</div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $summaryData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data) && count($data) >= 2) {
                $reportData = $data[0] ?? [];
                $summaryData = $data[1][0] ?? [];
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font name-col">Name</th>
                        <th class="small-font payment-dues-col">Payment Dues</th>
                        <th class="small-font credit-balance-col">Credit Balance</th>
                        <th class="small-font ledger-balance-col">Ledger Balance</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font name-col">{{ $row['full_name'] ?? '' }}</td>
                            <td class="small-font payment-dues-col">{{ number_format($row['due_amount'] ?? 0, 2) }}</td>
                            <td class="small-font credit-balance-col">{{ number_format($row['cr_bal'] ?? 0, 2) }}</td>
                            <td class="small-font ledger-balance-col">{{ number_format($row['ledger_bal'] ?? 0, 2) }}</td>
                        </tr>
                    @endforeach
                    
                    {{-- Total Row --}}
                    @if(!empty($summaryData))
                        <tr class="total-row">
                            <td class="small-font name-col" style="font-weight: bold;">Total</td>
                            <td class="small-font payment-dues-col" style="font-weight: bold;">{{ number_format($summaryData['total_payment_dues'] ?? 0, 2) }}</td>
                            <td class="small-font credit-balance-col" style="font-weight: bold;">{{ number_format($summaryData['total_credit_balance'] ?? 0, 2) }}</td>
                            <td class="small-font ledger-balance-col" style="font-weight: bold;">{{ number_format($summaryData['total_ledger_balance'] ?? 0, 2) }}</td>
                        </tr>
                    @endif
                </tbody>
            </table>

            {{-- Summary Table --}}
            @if(!empty($summaryData))
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 10px;">Summary</h3>
                    <table style="width: 70%; margin: 0;">
                        <thead>
                            <tr>
                                <th class="small-font text-center" style="width: 33.33%;">Payment Dues</th>
                                <th class="small-font text-center" style="width: 33.33%;">Credit Balance</th>
                                <th class="small-font text-center" style="width: 33.33%;">Ledger Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="small-font text-center">{{ number_format($summaryData['total_payment_dues'] ?? 0, 2) }}</td>
                                <td class="small-font text-center">{{ number_format($summaryData['total_credit_balance'] ?? 0, 2) }}</td>
                                <td class="small-font text-center">{{ number_format($summaryData['total_ledger_balance'] ?? 0, 2) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            @endif

        @else
            <div class="no-records">
                <p>No non-members receivable data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
