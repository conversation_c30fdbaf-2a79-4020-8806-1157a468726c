<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profit and Loss Statement</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 12px;
            line-height: 1.2;
        }

        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }

        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }

        .reg-number {
            font-size: 10px;
            margin: 2px 0;
        }

        .address {
            font-size: 10px;
            margin: 2px 0;
        }

        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }

        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 11px;
        }

        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 6px 4px;
        }

        .amount-cell {
            text-align: right;
            padding-right: 8px;
        }

        .account-cell {
            text-align: left;
            padding-left: 8px;
        }

        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .net-profit-row {
            font-weight: bold;
            background-color: #e6e6e6;
        }

        .report-date {
            margin-top: 10px;
            font-size: 10px;
        }

        .separator-column {
            width: 2%;
            border: none;
            background-color: white;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</div>
        <div class="reg-number">[REGN.NO:{{$company_details['society_reg_num'] ?? 'mumbau'}}]</div>
        <div class="address">{{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}</div>
        <div class="report-title">Income and Expenditure for {{$company_details['year'] ?? (isset($headings[2]) ? str_replace('_', '-', $headings[2]) : '2025-2026')}}</div>
    </div>

    <!-- Debug: Print actual data structure -->
    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; font-family: monospace; font-size: 10px;">
        <strong>DEBUG - Data Structure:</strong><br>
        <pre>{{ print_r($data, true) }}</pre>
        <br>
        <strong>DEBUG - Headings:</strong><br>
        <pre>{{ print_r($headings ?? 'No headings variable', true) }}</pre>
    </div>

    <!-- Profit and Loss Table -->
    <table class="table-container">
        <thead>
            <tr>
                @if(isset($headings))
                    @foreach($headings as $index => $heading)
                        @if($index == 3)
                            <th class="separator-column"></th>
                        @else
                            <th style="width: {{ $index == 0 || $index == 4 ? '25%' : '12%' }};">{{ $heading }}</th>
                        @endif
                    @endforeach
                @else
                    <th style="width: 25%;">Expense</th>
                    <th style="width: 12%;">2024-2025</th>
                    <th style="width: 12%;">2025-2026</th>
                    <th class="separator-column"></th>
                    <th style="width: 25%;">Income</th>
                    <th style="width: 12%;">2024-2025</th>
                    <th style="width: 12%;">2025-2026</th>
                @endif
            </tr>
        </thead>
        <tbody>
            @forelse ($data as $row)
                @php
                    // Extract values from array indices
                    $expenseAccount = $row[0] ?? '';
                    $expense2024 = is_numeric($row[1]) ? (float)$row[1] : 0;
                    $expense2025 = is_numeric($row[2]) ? (float)$row[2] : 0;
                    $incomeAccount = $row[4] ?? '';
                    $income2024 = is_numeric($row[5]) ? (float)$row[5] : 0;
                    $income2025 = is_numeric($row[6]) ? (float)$row[6] : 0;

                    // Check if this is a special row (Total or To Net Profit)
                    $isSpecialRow = (stripos($expenseAccount, 'total') !== false || stripos($expenseAccount, 'net profit') !== false);
                @endphp
                <tr class="{{ $isSpecialRow ? ($expenseAccount == 'To Net Profit' ? 'net-profit-row' : 'total-row') : '' }}">
                    <td class="account-cell">{{ $expenseAccount }}</td>
                    <td class="amount-cell">
                        @if($expense2024 != 0)
                            {{ number_format($expense2024, 2) }}
                        @else
                            0.00
                        @endif
                    </td>
                    <td class="amount-cell">
                        @if($expense2025 != 0)
                            {{ number_format($expense2025, 2) }}
                        @else
                            0.00
                        @endif
                    </td>
                    <td class="separator-column"></td>
                    <td class="account-cell">{{ $incomeAccount }}</td>
                    <td class="amount-cell">
                        @if($income2024 != 0)
                            {{ number_format($income2024, 2) }}
                        @else
                            0.00
                        @endif
                    </td>
                    <td class="amount-cell">
                        @if($income2025 != 0)
                            {{ number_format($income2025, 2) }}
                        @else
                            0.00
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" style="text-align: center;">No data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="report-date">
        Report Date: {{ date('jS F Y') }}
    </div>
</body>
</html>
