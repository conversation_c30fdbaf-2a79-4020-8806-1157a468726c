<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profit and Loss Statement</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 12px;
            line-height: 1.2;
        }

        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }

        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }

        .reg-number {
            font-size: 10px;
            margin: 2px 0;
        }

        .address {
            font-size: 10px;
            margin: 2px 0;
        }

        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }

        .table-container {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 11px;
        }

        .table-container th, .table-container td {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
        }

        .table-container th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 6px 4px;
        }

        .amount-cell {
            text-align: right;
            padding-right: 8px;
        }

        .account-cell {
            text-align: left;
            padding-left: 8px;
        }

        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .net-profit-row {
            font-weight: bold;
            background-color: #e6e6e6;
        }

        .report-date {
            margin-top: 10px;
            font-size: 10px;
        }

        .separator-column {
            width: 2%;
            border: none;
            background-color: white;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</div>
        <div class="reg-number">[REGN.NO:{{$company_details['society_reg_num'] ?? 'mumbau'}}]</div>
        <div class="address">{{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}</div>
        <div class="report-title">Income and Expenditure for {{$company_details['year'] ?? (isset($headings[2]) ? str_replace('_', '-', $headings[2]) : '2025-2026')}}</div>
    </div>

    <!-- Debug: Print actual data structure -->
    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; font-family: monospace; font-size: 10px;">
        <strong>DEBUG - Data Structure:</strong><br>
        <pre>{{ print_r($data, true) }}</pre>
        <br>
        <strong>DEBUG - Headings:</strong><br>
        <pre>{{ print_r($headings ?? 'No headings variable', true) }}</pre>
    </div>

    <!-- Profit and Loss Table -->
    <table class="table-container">
        <thead>
            <tr>
                @if(isset($headings))
                    @foreach($headings as $index => $heading)
                        @if($heading == '' || $heading == null)
                            <th class="separator-column"></th>
                        @else
                            @php
                                // Convert underscore to hyphen for display
                                $displayHeading = str_replace('_', '-', $heading);
                                // Determine column width based on content
                                $isAccountColumn = (stripos($heading, 'expense') !== false || stripos($heading, 'income') !== false);
                                $width = $isAccountColumn ? '25%' : '12%';
                            @endphp
                            <th style="width: {{ $width }};">{{ $displayHeading }}</th>
                        @endif
                    @endforeach
                @else
                    <th style="width: 25%;">Account</th>
                    <th style="width: 12%;">Amount</th>
                @endif
            </tr>
        </thead>
        <tbody>
            @forelse ($data as $row)
                @php
                    // Check if this is a special row (Total or To Net Profit)
                    $firstValue = $row[0] ?? '';
                    $isSpecialRow = (stripos($firstValue, 'total') !== false || stripos($firstValue, 'net profit') !== false);
                @endphp
                <tr class="{{ $isSpecialRow ? ($firstValue == 'To Net Profit' ? 'net-profit-row' : 'total-row') : '' }}">
                    @if(isset($headings))
                        @foreach($headings as $index => $heading)
                            @if($heading == '' || $heading == null)
                                <td class="separator-column"></td>
                            @else
                                @php
                                    $value = $row[$index] ?? '';
                                    $isAccountColumn = (stripos($heading, 'expense') !== false || stripos($heading, 'income') !== false);
                                @endphp
                                @if($isAccountColumn)
                                    <td class="account-cell">{{ $value }}</td>
                                @else
                                    <td class="amount-cell">
                                        @if(is_numeric($value) && $value != 0)
                                            {{ number_format((float)$value, 2) }}
                                        @else
                                            0.00
                                        @endif
                                    </td>
                                @endif
                            @endif
                        @endforeach
                    @else
                        <td class="account-cell">{{ $row[0] ?? '' }}</td>
                        <td class="amount-cell">{{ is_numeric($row[1] ?? '') ? number_format((float)$row[1], 2) : '0.00' }}</td>
                    @endif
                </tr>
            @empty
                <tr>
                    <td colspan="{{ isset($headings) ? count($headings) : 2 }}" style="text-align: center;">No data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="report-date">
        Report Date: {{ date('jS F Y') }}
    </div>
</body>
</html>
