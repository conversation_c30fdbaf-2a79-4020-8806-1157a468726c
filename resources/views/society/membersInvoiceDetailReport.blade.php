<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
    </style>
</head>
<body>
    

    
    @if(isset($report_title))
    <div class="sub-title">{{ $report_title }}</div>
    @endif
    
    @if(isset($date_range))
    <div class="text-center">
        <strong>Date: {{ $date_range }}</strong>
    </div>
    @endif
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];
                
                // Define custom order for columns to match PDF layout exactly
                $orderedHeaders = [
                    'sr_no',
                    'unit_name',
                    'bill_to',
                    'gstin',
                    'its_no',
                    'invoice_number',
                    'interest',
                    'tax',
                    'invoice_amount',
                    'p_arrears',
                    'i_arrears',
                    'credit_adjustment',
                    'payable',
                    'receipt',
                    'net_due',
                    'advance_credit'
                ];
                
                // Get headers from first row and order them
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $allHeaders = array_keys($firstRow);
                        
                        // Filter and order headers based on what exists in data or should be shown
                        foreach ($orderedHeaders as $orderedHeader) {
                            if (in_array($orderedHeader, $allHeaders) || in_array($orderedHeader, ['its_no', 'credit_adjustment'])) {
                                $headers[] = $orderedHeader;
                            }
                        }

                        // Add any remaining headers that weren't in our ordered list
                        foreach ($allHeaders as $header) {
                            if (!in_array($header, $headers) && !in_array($header, ['period'])) {
                                $headers[] = $header;
                            }
                        }
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        <th colspan="{{ count($headers) }}" style="text-align:center;padding:10px">
    <div style="text-align:center; margin:auto;">
        <h2 style="margin-bottom:0;margin-top:5px; text-align:center;">{{$company_details['society_name'] ?? 'SOCIETY NAME'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px; text-align:center;">
            Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}
        </h3>
        @if(isset($company_details['society_address']))
        <p style="margin-bottom:0;margin-top:5px; text-align:center;">{{$company_details['society_address']}}</p>
        @endif
    </div>
</th>
                    </tr>
                    <tr>
                        @foreach($headers as $header)
                            <th class="small-font">
                                @switch($header)
                                    @case('sr_no')
                                        Sr No.
                                        @break
                                    @case('unit_name')
                                        Unit Name
                                        @break
                                    @case('bill_to')
                                        Bill To
                                        @break
                                    @case('gstin')
                                        GSTIN
                                        @break
                                    @case('its_no')
                                        ITS No.
                                        @break
                                    @case('invoice_number')
                                        Invoice Number
                                        @break
                                    @case('interest')
                                        Interest
                                        @break
                                    @case('tax')
                                        Tax
                                        @break
                                    @case('invoice_amount')
                                        Invoice Amount
                                        @break
                                    @case('p_arrears')
                                        Principal Arrears
                                        @break
                                    @case('i_arrears')
                                        I Arrears
                                        @break
                                    @case('credit_adjustment')
                                        Credit/Adjustment
                                        @break
                                    @case('payable')
                                        Payable
                                        @break
                                    @case('receipt')
                                        Receipt
                                        @break
                                    @case('net_due')
                                        Net Due
                                        @break
                                    @case('advance_credit')
                                        Advance/Credit
                                        @break
                                    @default
                                        {{ ucwords(str_replace('_', ' ', $header)) }}
                                @endswitch
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                <td class="{{ in_array($header, ['interest', 'tax', 'invoice_amount', 'p_arrears', 'i_arrears', 'credit_adjustment', 'payable', 'receipt', 'net_due', 'advance_credit']) ? 'text-right small-font' : 'small-font' }}">
                                    @if($header === 'its_no')
                                        {{-- ITS No field - always empty as per data structure --}}
                                    @elseif($header === 'credit_adjustment')
                                        {{ number_format(0, 2) }}
                                    @elseif(in_array($header, ['interest', 'tax', 'invoice_amount', 'p_arrears', 'i_arrears', 'payable', 'receipt', 'net_due', 'advance_credit']) && isset($row[$header]) && is_numeric($row[$header]))
                                        {{ number_format($row[$header], 2) }}
                                    @else
                                        {{ $row[$header] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        @if(isset($summaryData['total_invoices']))
                        <th class="text-center">Total Invoices</th>
                        @endif
                        @if(isset($summaryData['invoice_preiod']))
                        <th class="text-center">Invoice Period</th>
                        @endif
                        @if(isset($summaryData['total_payable']))
                        <th class="text-center">Total Payable</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['total_invoices']))
                        <td class="text-center">{{ $summaryData['total_invoices'] }}</td>
                        @endif
                        @if(isset($summaryData['invoice_preiod']))
                        <td class="text-center">{{ $summaryData['invoice_preiod'] }}</td>
                        @endif
                        @if(isset($summaryData['total_payable']))
                        <td class="text-center">{{ $summaryData['total_payable'] }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
