<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FUTURESCAPE TECHNOLOGIES PVT LTD - Members</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
    </style>
</head>
<body>
<div class="printOption">
        <p>{{ 'Income Receipt Report' ?? 'Society Report' }}</p>
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        <table>
            <thead>
                <tr>
                    <th colspan="10" style="text-align:center;padding:10px">
    <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'BHAKTI PARK PHASE-II BLDG NO.1 CHS LTD'}}</h2>
    <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'TNA/(TNA)/HSG/(TC)22641/2011'}}
    @if(isset($company_details['gstin'])) | GSTIN/UIN: {{$company_details['gstin']}} @endif</h3>
    @if(isset($company_details['society_address']))
    <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address']}}</p>
    @endif
</th>
                </tr>
                <tr>
                    <th>Payment Date</th>
                    <th>Payment Number</th>
                    <th>Payment Mode</th>
                    <th>Bill Type</th>
                    <th>Invoice Number</th>
                    <th>Payment Reference</th>
                    <th>Vendor Name</th>
                    <th>Payment Amount</th>
                    <th>Writeoff amount</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                    @foreach($data as $member)
                        <tr>
                            <td>{{ $member['payment_date'] }}</td>
                            <td>{{ $member['payment_number'] ?? '' }}</td>
                            <td>{{ $member['payment_mode'] ?? '' }}</td>
                            <td>{{ $member['bill_type'] ?? '' }}</td>
                            <td>{{ $member['invoice_number'] ?? '' }}</td>
                            <td>{{ $member['transaction_reference'] ?? '' }}</td>
                            <td>{{ $member['vendor_name'] ?? '' }}</td>
                            <td>{{ $member['payment_amount'] ?? '' }}</td>
                            <td>{{ $member['writeoff_amount'] ?? '' }}</td>
                            <td>{{ $member['status'] ?? '' }}</td>
                            <td></td>
                        </tr>
                    @endforeach
            </tbody>
        </table>
    </div>
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html> 