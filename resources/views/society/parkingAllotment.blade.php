<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking Allotment Report</title>
    <style>
        @page {
            margin: 15mm;
            size: A4 landscape;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }
        
        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }
        
        .parking-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 9px;
        }
        
        .parking-table th,
        .parking-table td {
            border: 1px solid #000;
            padding: 4px 3px;
            vertical-align: top;
        }
        
        .parking-table th {
            background-color: #e8e8e8;
            font-weight: bold;
            text-align: center;
            font-size: 9px;
        }
        
        .parking-row td {
            text-align: left;
            padding: 3px 4px;
        }
        
        .center-cell {
            text-align: center;
        }
        
        .status-allotted {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-unallotted {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .report-date {
            margin-top: 15px;
            font-size: 8px;
            text-align: right;
        }
        
        .building-header {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Debug: Print all received data -->
    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; font-family: monospace; font-size: 8px;">
        <strong>DEBUG - Data Structure:</strong><br>
        <pre>{{ print_r($data ?? 'No data variable', true) }}</pre>
        <br>
        <strong>DEBUG - Company Details:</strong><br>
        <pre>{{ print_r($company_details ?? 'No company_details variable', true) }}</pre>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT LTD'}}</div>
        <div class="report-title">Parking Allotment Detail Report</div>
    </div>

    <!-- Parking Allotment Table -->
    <table class="parking-table">
        <thead>
            <tr>
                <th style="width: 8%;">Sr. No.</th>
                <th style="width: 12%;">Building/Unit</th>
                <th style="width: 15%;">Member Name</th>
                <th style="width: 10%;">Parking Number</th>
                <th style="width: 10%;">Parking Type</th>
                <th style="width: 8%;">Vehicle Type</th>
                <th style="width: 8%;">Allowed</th>
                <th style="width: 8%;">Actual</th>
                <th style="width: 8%;">Status</th>
                <th style="width: 13%;">Effective Date</th>
            </tr>
        </thead>
        <tbody>
            @php
                $currentBuilding = '';
                $serialNumber = 1;
            @endphp
            
            @forelse($data as $parking)
                @php
                    $buildingName = $parking['soc_building_name'] ?? 'Unknown Building';
                @endphp
                
                {{-- Building Header Row (if building changes) --}}
                @if($currentBuilding !== $buildingName)
                    @php $currentBuilding = $buildingName; @endphp
                    <tr class="building-header">
                        <td colspan="10">{{ $buildingName }}</td>
                    </tr>
                @endif
                
                {{-- Parking Row --}}
                <tr class="parking-row">
                    <td class="center-cell">{{ $serialNumber++ }}</td>
                    <td>{{ $parking['allotment_to'] ?? ($buildingName . ' / ' . ($parking['unit_name'] ?? '')) }}</td>
                    <td>{{ $parking['member_primary_name'] ?? 'N/A' }}</td>
                    <td class="center-cell">{{ $parking['parking_number'] ?? 'N/A' }}</td>
                    <td>{{ $parking['allotted_parking_type'] ?? $parking['parking_type'] ?? 'N/A' }}</td>
                    <td class="center-cell">{{ $parking['allotment_for'] ?? 'N/A' }}</td>
                    <td class="center-cell">{{ $parking['allowed_number_of_parkings'] ?? 0 }}</td>
                    <td class="center-cell">{{ $parking['actual_number_of_parkings'] ?? 0 }}</td>
                    <td class="center-cell status-{{ strtolower($parking['status'] ?? 'unknown') }}">
                        {{ ucfirst($parking['status'] ?? 'Unknown') }}
                    </td>
                    <td class="center-cell">{{ $parking['effective_date'] ?? 'N/A' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" style="text-align: center; font-style: italic;">No parking allotment data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    
    <div class="report-date">
        Report Generated: {{ date('jS F Y, g:i A') }}
    </div>
</body>
</html>
