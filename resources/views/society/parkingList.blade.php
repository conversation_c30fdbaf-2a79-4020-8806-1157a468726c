<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking List Report</title>
    <style>
        @page {
            margin: 15mm;
            size: A4 landscape;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }
        
        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }
        
        .parking-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            font-size: 9px;
        }
        
        .parking-table th,
        .parking-table td {
            border: 1px solid #000;
            padding: 4px 3px;
            vertical-align: top;
        }
        
        .parking-table th {
            background-color: #e8e8e8;
            font-weight: bold;
            text-align: center;
            font-size: 9px;
        }
        
        .unit-header {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
        
        .parking-row td {
            text-align: left;
            padding: 3px 4px;
        }
        
        .center-cell {
            text-align: center;
        }
        
        .alloted-yes {
            background-color: #d4edda;
            color: #155724;
        }
        
        .alloted-no {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .report-date {
            margin-top: 15px;
            font-size: 8px;
            text-align: right;
        }
    </style>
</head>
<body>

    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT LTD'}}</div>
        <div class="report-title">Parking List Report</div>
    </div>

    <!-- Parking List Table -->
    <table class="parking-table">
        <thead>
            <tr>
                <th style="width: 8%;">Sr. No.</th>
                <th style="width: 12%;">Building/Unit</th>
                <th style="width: 15%;">Member Name</th>
                <th style="width: 10%;">Parking Number</th>
                <th style="width: 8%;">Badge Number</th>
                <th style="width: 12%;">Vehicle Registration</th>
                <th style="width: 8%;">Vehicle Type</th>
                <th style="width: 10%;">Vehicle Company</th>
                <th style="width: 8%;">Vehicle Color</th>
                <th style="width: 8%;">Allotted</th>
                <th style="width: 10%;">Effective Date</th>
            </tr>
        </thead>
        <tbody>
            @php
                $serialNumber = 1;
            @endphp
            
            @forelse($data as $unit)
                {{-- Unit Header Row --}}
                <tr class="unit-header">
                    <td colspan="11">
                        {{ $unit['soc_building_name'] ?? 'Unknown Building' }} - {{ $unit['unit_flat_number'] ?? 'Unknown Unit' }}
                        @if(!empty($unit['member_name']))
                            ({{ $unit['member_name'] }})
                        @endif
                    </td>
                </tr>
                
                {{-- Parking entries for this unit --}}
                @if(isset($unit['rows']) && is_array($unit['rows']))
                    @foreach($unit['rows'] as $parking)
                        <tr class="parking-row">
                            <td class="center-cell">{{ $serialNumber++ }}</td>
                            <td>{{ $parking['soc_building_name'] ?? '' }} - {{ $parking['unit_flat_number'] ?? '' }}</td>
                            <td>{{ $parking['member_name'] ?? 'N/A' }}</td>
                            <td class="center-cell">{{ $parking['parking_number'] ?? 'N/A' }}</td>
                            <td class="center-cell">{{ $parking['badge_number'] ?? 'N/A' }}</td>
                            <td class="center-cell">{{ $parking['vehicle_registration_number'] ?? 'N/A' }}</td>
                            <td class="center-cell">{{ $parking['vehicle_type'] ?? 'N/A' }}</td>
                            <td>{{ trim($parking['vehicle_company'] ?? '') ?: 'N/A' }}</td>
                            <td class="center-cell">{{ trim($parking['vehicle_colour'] ?? '') ?: 'N/A' }}</td>
                            <td class="center-cell alloted-{{ strtolower($parking['is_alloted'] ?? 'no') }}">
                                {{ ucfirst($parking['is_alloted'] ?? 'No') }}
                            </td>
                            <td class="center-cell">
                                @if(!empty($parking['effective_date']))
                                    {{ date('d-m-Y', strtotime($parking['effective_date'])) }}
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                    @endforeach
                @else
                    <tr class="parking-row">
                        <td colspan="11" style="text-align: center; font-style: italic;">No parking entries found for this unit</td>
                    </tr>
                @endif
            @empty
                <tr>
                    <td colspan="11" style="text-align: center; font-style: italic;">No parking data available</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    
    <div class="report-date">
        Report Generated: {{ date('jS F Y, g:i A') }}
    </div>
</body>
</html>
