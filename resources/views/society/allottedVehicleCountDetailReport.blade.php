<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Allotted Vehicle Count Detail Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #ddd; text-align: left; font-size: 11px; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 10px; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Allotted Vehicle Count Detail Report' ?? 'Society Report' }}</p>
        
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'Society Name'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">[REGN.NO:{{$company_details['society_reg_num'] ?? 'Registration Number'}}]</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Registered Vehicle Count Detail</div>

    <div class="text-center">
        <strong>Filter: All</strong>
    </div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];
            @endphp
            
            @if(!empty($reportData))
                <table>
                    <thead>
                        <tr>
                            <th class="small-font">Building/Unit</th>
                            <th class="small-font text-center">Shaded Parking</th>
                            <th class="small-font text-center">Open Parking</th>
                            <th class="small-font text-center">2wheeler (Owner/Tenant)</th>
                            <th class="small-font text-center">4wheeler (Owner/Tenant)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($reportData as $row)
                            <tr>
                                <td class="small-font">{{ $row['building_unit'] ?? '' }}</td>
                                <td class="small-font text-center">{{ $row['shaded_parking'] ?: 0 }}</td>
                                <td class="small-font text-center">{{ $row['open_parking'] ?? 0 }}</td>
                                <td class="small-font text-center">{{ $row['two_wheeler_owner_tenant'] ?? '0|0' }}</td>
                                <td class="small-font text-center">{{ $row['four_wheeler_owner_tenant'] ?? '0|0' }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @endif
        @else
            <div class="text-center" style="padding: 20px;">
                <p>No data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        <th class="text-center">Total Units</th>
                        <th class="text-center">Shaded Parking</th>
                        <th class="text-center">Open Parking</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">{{ $summaryData['Total'] ?? 0 }}</td>
                        <td class="text-center">{{ $summaryData['Shaded'] ?? 0 }}</td>
                        <td class="text-center">{{ $summaryData['Open'] ?? 0 }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    @endif

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
