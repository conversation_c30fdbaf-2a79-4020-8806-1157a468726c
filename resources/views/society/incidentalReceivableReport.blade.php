<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Incidental Receivable Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        @page {
            margin: 8mm;
            size: A4 landscape;
        }

        html {
            font-size: 62.5%;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 8px;
            margin: 0;
            color: #333;
            background: #fff;
            line-height: 1.1;
        }

        .page-header {
            text-align: center;
            margin-bottom: 12px;
            border-bottom: 1px solid #000;
            padding-bottom: 6px;
        }

        .company-name {
            font-size: 11px;
            font-weight: bold;
            margin: 0;
        }

        .company-details {
            font-size: 8px;
            margin: 1px 0;
        }

        .report-title {
            font-size: 9px;
            font-weight: bold;
            margin-top: 4px;
        }

        table {
            background: white;
            border: solid 1px #000;
            width: 100%;
            margin: 8px 0;
            border-collapse: collapse;
            font-size: 6px;
        }

        table th, table td {
            padding: 2px 1px;
            border: 1px solid #000;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
            overflow: hidden;
        }

        table thead {
            background-color: #e8e8e8;
        }

        table th {
            font-weight: bold;
            text-align: center;
            vertical-align: top;
            font-size: 6px;
            line-height: 1.0;
        }

        .printOption {
            background: #001941;
            height: 25px;
            padding: 6px;
            color: #fff;
            font-size: 8px;
        }

        .footer {
            background: #001941;
            padding: 6px;
            color: #fff;
            text-align: right;
            font-size: 7px;
            margin-top: 10px;
        }

        .report-date {
            text-align: left;
            margin-top: 10px;
            font-weight: bold;
            font-size: 7px;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .no-records {
            text-align: center;
            padding: 15px;
            font-style: italic;
            color: #666;
        }

        /* Optimized column widths for landscape */
        .unit-col {
            width: 6%;
            text-align: left;
            font-size: 6px;
        }

        .member-col {
            width: 8%;
            text-align: left;
            font-size: 6px;
        }

        .amount-col {
            width: 3.5%;
            text-align: right;
            font-size: 6px;
        }

        .total-col {
            width: 4%;
            text-align: right;
            font-weight: bold;
            font-size: 6px;
        }

        .header-text {
            font-size: 5px;
            line-height: 0.9;
            word-wrap: break-word;
            text-align: center;
            padding: 1px;
        }

        .data-cell {
            font-size: 6px;
            line-height: 1.0;
        }
    </style>
</head>
<body>
    <div class="printOption">
        <p>Incidental Receivable Report</p>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</div>
        <div class="company-details">Reg No: {{$company_details['society_reg_num'] ?? 'mumbau'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</div>
        <div class="company-details">{{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}</div>
        <div class="report-title">Incidental Receivable Report</div>
    </div>

    <div style="width: 100%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }

            // Define column headers with shortened names for better display
            $columnMappings = [
                'Building / Unit' => 'Building / Unit',
                'Primary Member' => 'Primary Member',
                'Move In' => 'Move In',
                'Move Out' => 'Move Out',
                'Renovation' => 'Renovation',
                'Delayed Payment Charges' => 'Delayed Payment Charges',
                'Civil Engineer Visit Charges - Members' => 'Civil Engineer Visit Charges - Members',
                'Cheque Return Charges' => 'Cheque Return Charges',
                'Penalty - Water Overflow' => 'Penalty - Water Overflow',
                'Scrap Sale' => 'Scrap Sale',
                'Principal Arrears' => 'Principal Arrears',
                'Transfer Fee' => 'Transfer Fee',
                'Major Repair Fund - Painting' => 'Major Repair Fund - Painting',
                'Removing Illegal Shed On Parking' => 'Removing Illegal Shed on Parking',
                'Penalty  Keeping Goods Out Of Shop' => 'Penalty Keeping Goods out of Shop',
                'Penalty -  Without Permission Civil Repairing And Don\'t Follow Covid Guidelines' => 'Penalty - Without Permission Civil Repairing',
                'Tenant Shifting Charges' => 'Tenant Shifting Charges',
                'Drill Work At Late Evening' => 'Drill Work At Late Evening',
                'Parking Charges' => 'Parking Charges',
                'Form Fees' => 'Form Fees',
                'Penalty For Parking' => 'Penalty For Parking',
                'Legal Notice Charges' => 'Legal Notice Charges',
                'Repair Charges Of Solar' => 'Repair Charges Of Solar',
                'Society Premises Charges' => 'Society Premises Charges',
                'Penalty  Ac Installation Without Permission' => 'Penalty AC Installation Without Permission',
                'Penalty  Damage To Society Property' => 'Penalty Damage To Society Property',
                'Penalty For Chair Damage' => 'Penalty For Chair Damage',
                'Penalty Flat Rented Without Permission' => 'Penalty Flat Rented Without Permission',
                'Membership Fees' => 'Membership Fees',
                'Noc Charges' => 'NOC Charges',
                'Civil Work Charges' => 'Civil Work Charges',
                'Contribution For Navratri And Ganpati Festival' => 'Contribution For Navratri And Ganpati Festival',
                'Conveyance Deed Charges' => 'Conveyance Deed Charges',
                'Share Transfer Fees' => 'Share Transfer Fees',
                'Penalty For Civil Work' => 'Penalty For Civil Work',
                'Miscellaneous Income' => 'Miscellaneous Income',
                'Penalty Charges For Staircase' => 'Penalty Charges For Staircase',
                'Abc' => 'ABC',
                'Advertisement Income' => 'Advertisement Income',
                'Total Due' => 'Total Due'
            ];
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        @foreach($columnMappings as $originalKey => $displayName)
                            @if($originalKey == 'Building / Unit')
                                <th class="unit-col header-text">{{ $displayName }}</th>
                            @elseif($originalKey == 'Primary Member')
                                <th class="member-col header-text">{{ $displayName }}</th>
                            @elseif($originalKey == 'Total Due')
                                <th class="total-col header-text">{{ $displayName }}</th>
                            @else
                                <th class="amount-col header-text">{{ $displayName }}</th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($columnMappings as $originalKey => $displayName)
                                @if($originalKey == 'Building / Unit')
                                    <td class="small-font unit-col">{{ $row[$originalKey] ?? '' }}</td>
                                @elseif($originalKey == 'Primary Member')
                                    <td class="small-font member-col">{{ $row[$originalKey] ?? '' }}</td>
                                @elseif($originalKey == 'Total Due')
                                    <td class="small-font total-col">{{ number_format($row[$originalKey] ?? 0, 2) }}</td>
                                @else
                                    <td class="small-font amount-col">{{ number_format($row[$originalKey] ?? 0, 2) }}</td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="no-records">
                <p>No incidental receivable data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
