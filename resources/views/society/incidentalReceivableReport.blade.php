<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Incidental Receivable Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; min-width: 2000px; }
        table th, table td { padding: 4px; border: 1px solid #000; text-align: left; font-size: 8px; white-space: nowrap; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; vertical-align: top; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 7px; }
        .unit-col { width: 80px; text-align: left; }
        .member-col { width: 120px; text-align: left; }
        .amount-col { width: 60px; text-align: right; }
        .total-col { width: 80px; text-align: right; font-weight: bold; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
        .table-container { overflow-x: auto; width: 100%; }
        .header-text { font-size: 7px; line-height: 1.1; word-wrap: break-word; max-width: 60px; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Incidental Receivable Report' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Incidental Receivable Report</div>

    <div class="table-container" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }

            // Define column headers with shortened names for better display
            $columnMappings = [
                'Building / Unit' => 'Building / Unit',
                'Primary Member' => 'Primary Member',
                'Move In' => 'Move In',
                'Move Out' => 'Move Out',
                'Renovation' => 'Renovation',
                'Delayed Payment Charges' => 'Delayed Payment Charges',
                'Civil Engineer Visit Charges - Members' => 'Civil Engineer Visit Charges - Members',
                'Cheque Return Charges' => 'Cheque Return Charges',
                'Penalty - Water Overflow' => 'Penalty - Water Overflow',
                'Scrap Sale' => 'Scrap Sale',
                'Principal Arrears' => 'Principal Arrears',
                'Transfer Fee' => 'Transfer Fee',
                'Major Repair Fund - Painting' => 'Major Repair Fund - Painting',
                'Removing Illegal Shed On Parking' => 'Removing Illegal Shed on Parking',
                'Penalty  Keeping Goods Out Of Shop' => 'Penalty Keeping Goods out of Shop',
                'Penalty -  Without Permission Civil Repairing And Don\'t Follow Covid Guidelines' => 'Penalty - Without Permission Civil Repairing',
                'Tenant Shifting Charges' => 'Tenant Shifting Charges',
                'Drill Work At Late Evening' => 'Drill Work At Late Evening',
                'Parking Charges' => 'Parking Charges',
                'Form Fees' => 'Form Fees',
                'Penalty For Parking' => 'Penalty For Parking',
                'Legal Notice Charges' => 'Legal Notice Charges',
                'Repair Charges Of Solar' => 'Repair Charges Of Solar',
                'Society Premises Charges' => 'Society Premises Charges',
                'Penalty  Ac Installation Without Permission' => 'Penalty AC Installation Without Permission',
                'Penalty  Damage To Society Property' => 'Penalty Damage To Society Property',
                'Penalty For Chair Damage' => 'Penalty For Chair Damage',
                'Penalty Flat Rented Without Permission' => 'Penalty Flat Rented Without Permission',
                'Membership Fees' => 'Membership Fees',
                'Noc Charges' => 'NOC Charges',
                'Civil Work Charges' => 'Civil Work Charges',
                'Contribution For Navratri And Ganpati Festival' => 'Contribution For Navratri And Ganpati Festival',
                'Conveyance Deed Charges' => 'Conveyance Deed Charges',
                'Share Transfer Fees' => 'Share Transfer Fees',
                'Penalty For Civil Work' => 'Penalty For Civil Work',
                'Miscellaneous Income' => 'Miscellaneous Income',
                'Penalty Charges For Staircase' => 'Penalty Charges For Staircase',
                'Abc' => 'ABC',
                'Advertisement Income' => 'Advertisement Income',
                'Total Due' => 'Total Due'
            ];
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        @foreach($columnMappings as $originalKey => $displayName)
                            @if($originalKey == 'Building / Unit')
                                <th class="small-font unit-col header-text">{{ $displayName }}</th>
                            @elseif($originalKey == 'Primary Member')
                                <th class="small-font member-col header-text">{{ $displayName }}</th>
                            @elseif($originalKey == 'Total Due')
                                <th class="small-font total-col header-text">{{ $displayName }}</th>
                            @else
                                <th class="small-font amount-col header-text">{{ $displayName }}</th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($columnMappings as $originalKey => $displayName)
                                @if($originalKey == 'Building / Unit')
                                    <td class="small-font unit-col">{{ $row[$originalKey] ?? '' }}</td>
                                @elseif($originalKey == 'Primary Member')
                                    <td class="small-font member-col">{{ $row[$originalKey] ?? '' }}</td>
                                @elseif($originalKey == 'Total Due')
                                    <td class="small-font total-col">{{ number_format($row[$originalKey] ?? 0, 2) }}</td>
                                @else
                                    <td class="small-font amount-col">{{ number_format($row[$originalKey] ?? 0, 2) }}</td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="no-records">
                <p>No incidental receivable data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
