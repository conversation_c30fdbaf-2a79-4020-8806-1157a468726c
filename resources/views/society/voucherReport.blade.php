<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Voucher Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .sr-col { width: 8%; text-align: center; }
        .voucher-type-col { width: 15%; text-align: center; }
        .date-col { width: 12%; text-align: center; }
        .from-ledger-col { width: 25%; text-align: left; }
        .to-ledger-col { width: 25%; text-align: left; }
        .amount-col { width: 15%; text-align: right; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Voucher Report' ?? 'Society Report' }}</p>
    </div>
    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'RIVER WALK CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Voucher Report</div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font sr-col">Sr.No</th>
                        <th class="small-font voucher-type-col">Voucher Type</th>
                        <th class="small-font date-col">Transaction Date</th>
                        <th class="small-font from-ledger-col">From Ledger</th>
                        <th class="small-font to-ledger-col">To Ledger</th>
                        <th class="small-font amount-col">Amount</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font sr-col">{{ $row['Sr.No'] ?? '' }}</td>
                            <td class="small-font voucher-type-col">{{ ucwords($row['voucher_type'] ?? '') }}</td>
                            <td class="small-font date-col">{{ date('d/m/Y', strtotime($row['transaction_date']     ?? '')) }}</td>
                            <td class="small-font from-ledger-col">{{ $row['from_ledger'] ?? '' }}</td>
                            <td class="small-font to-ledger-col">{{ $row['to_ledger'] ?? '' }}</td>
                            <td class="small-font amount-col">{{ number_format($row['amount'] ?? 0, 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="no-records">
                <p>No records found</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
