<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Non Member Advances Report</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }
        
        .company-details {
            font-size: 10px;
            margin: 2px 0;
        }
        
        .report-title {
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
            color: #8B0000;
        }
        
        .advances-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto 20px auto;
            font-size: 10px;
        }
        
        .advances-table th,
        .advances-table td {
            border: 1px solid #000;
            padding: 6px 8px;
            vertical-align: top;
        }
        
        .advances-table th {
            background-color: #e8e8e8;
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
        
        .name-cell {
            text-align: left;
            width: 40%;
        }
        
        .amount-cell {
            text-align: right;
            width: 30%;
        }
        
        .total-row {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .summary-section {
            margin-top: 30px;
        }
        
        .summary-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .summary-table {
            width: 60%;
            margin: 0 auto;
            border-collapse: collapse;
        }
        
        .summary-table th,
        .summary-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 11px;
        }
        
        .summary-table th {
            background-color: #e8e8e8;
            font-weight: bold;
        }
        
        .report-date {
            margin-top: 20px;
            font-size: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'River Walk CHS'}}</div>
        <div class="company-details">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</div>
        <div class="company-details">{{$company_details['society_address'] ?? 'Society Address'}}</div>
        <div class="report-title">Non Member Advance</div>
    </div>

    <!-- Non Member Advances Table -->
    <table class="advances-table">
        <thead>
            <tr>
                <th class="name-cell">Name</th>
                <th class="amount-cell">Refundable Balance</th>
                <th class="amount-cell">Adjustable Balance</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalRefundable = 0;
                $totalAdjustable = 0;
                $dataRows = [];
                $totalRow = null;
            @endphp
            
            @forelse($data as $row)
                @php
                    // Check if this is the total row
                    $isTotal = (strtolower($row['name'] ?? '') === 'total');
                    
                    if ($isTotal) {
                        $totalRow = $row;
                    } else {
                        $dataRows[] = $row;
                        $totalRefundable += (float)($row['total_refundable'] ?? 0);
                        $totalAdjustable += (float)($row['total_adjustable'] ?? 0);
                    }
                @endphp
            @empty
            @endforelse
            
            {{-- Display data rows --}}
            @foreach($dataRows as $row)
                <tr>
                    <td class="name-cell">{{ $row['name'] ?? 'N/A' }}</td>
                    <td class="amount-cell">{{ number_format((float)($row['total_refundable'] ?? 0), 2) }}</td>
                    <td class="amount-cell">{{ number_format((float)($row['total_adjustable'] ?? 0), 2) }}</td>
                </tr>
            @endforeach
            
            {{-- Display total row --}}
            @if($totalRow)
                <tr class="total-row">
                    <td class="name-cell">{{ strtoupper($totalRow['name']) }}</td>
                    <td class="amount-cell">{{ number_format((float)($totalRow['total_refundable'] ?? 0), 2) }}</td>
                    <td class="amount-cell">{{ number_format((float)($totalRow['total_adjustable'] ?? 0), 2) }}</td>
                </tr>
            @else
                {{-- Calculate and display total if not provided --}}
                <tr class="total-row">
                    <td class="name-cell">SUM</td>
                    <td class="amount-cell">{{ number_format($totalRefundable, 2) }}</td>
                    <td class="amount-cell">{{ number_format($totalAdjustable, 2) }}</td>
                </tr>
            @endif
            
            @if(empty($data))
                <tr>
                    <td colspan="3" style="text-align: center; font-style: italic;">No data available</td>
                </tr>
            @endif
        </tbody>
    </table>
    
    <!-- Summary Section -->
    <div class="summary-section">
        <div class="summary-title">Summary</div>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Refundable Balance</th>
                    <th>Adjustable Balance</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ number_format($totalRow ? (float)($totalRow['total_refundable'] ?? 0) : $totalRefundable, 2) }}</td>
                    <td>{{ number_format($totalRow ? (float)($totalRow['total_adjustable'] ?? 0) : $totalAdjustable, 2) }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>
</body>
</html>
