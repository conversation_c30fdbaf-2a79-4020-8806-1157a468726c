<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        @page {
            margin: 10mm;
            size: A4 landscape;
        }

        html {
            font-size: 62.5%;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 9px;
            margin: 0;
            color: #333;
            background: #fff;
            line-height: 1.2;
        }

        .page-header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #000;
            padding-bottom: 8px;
        }

        .company-name {
            font-size: 12px;
            font-weight: bold;
            margin: 0;
        }

        .company-details {
            font-size: 9px;
            margin: 2px 0;
        }

        .report-title {
            font-size: 10px;
            font-weight: bold;
            margin-top: 5px;
        }

        table {
            background: white;
            border: solid 1px #000;
            width: 100%;
            margin: 10px 0;
            border-collapse: collapse;
            font-size: 8px;
        }

        table th, table td {
            padding: 3px 2px;
            border: 1px solid #000;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
        }

        table thead {
            background-color: #e8e8e8;
        }

        table th {
            font-weight: bold;
            font-size: 8px;
            text-align: center;
        }

        .printOption {
            background: #001941;
            height: 30px;
            padding: 8px;
            color: #fff;
            font-size: 10px;
        }

        .footer {
            background: #001941;
            padding: 8px;
            color: #fff;
            text-align: right;
            font-size: 8px;
            margin-top: 15px;
        }

        .summary-section {
            margin-top: 20px;
        }

        .summary-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 10px;
        }

        .summary-table {
            background-color: #f9f9f9;
            width: 50%;
            margin: 0 auto;
        }

        .summary-table th, .summary-table td {
            background-color: #f9f9f9;
            font-weight: bold;
            text-align: center;
            padding: 5px;
        }

        .total-row {
            background-color: #e9e9e9;
            font-weight: bold;
        }

        .report-date {
            text-align: left;
            margin-top: 15px;
            font-weight: bold;
            font-size: 8px;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        /* Column width optimization for landscape */
        .col-vendor-name { width: 12%; }
        .col-vendor-address { width: 15%; }
        .col-bill-number { width: 8%; }
        .col-pan-number { width: 10%; }
        .col-company-type { width: 8%; }
        .col-section { width: 6%; }
        .col-date { width: 8%; }
        .col-amount { width: 8%; text-align: right; }
        .col-rate { width: 5%; text-align: center; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>TDS Payable Report</p>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="company-name">{{$company_details['society_name'] ?? 'FUTURESCAPE TECHNOLOGIES PVT'}}</div>
        <div class="company-details">Reg No: {{$company_details['society_reg_num'] ?? 'mumbau'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</div>
        <div class="company-details">{{$company_details['society_address'] ?? 'S Pranavanandji Marg., Sector 30, Vashi, CIDCO Exhibition Center, Navi Mumbai, Maharashtra- 400703'}}</div>
        <div class="report-title">TDS Payable Report</div>
        @if(isset($date_range))
            <div class="company-details"><strong>Date: {{ $date_range }}</strong></div>
        @endif
    </div>
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];
                
                // Get dynamic headers from first row of data
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $headers = array_keys($firstRow);
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            @if($header !== 'id')
                                @php
                                    $columnClass = '';
                                    switch($header) {
                                        case 'vendor_name':
                                            $columnClass = 'col-vendor-name';
                                            break;
                                        case 'vendor_address':
                                            $columnClass = 'col-vendor-address';
                                            break;
                                        case 'vendor_bill_num':
                                            $columnClass = 'col-bill-number';
                                            break;
                                        case 'vendor_pan_num':
                                            $columnClass = 'col-pan-number';
                                            break;
                                        case 'vendor_is_company':
                                            $columnClass = 'col-company-type';
                                            break;
                                        case 'section':
                                            $columnClass = 'col-section';
                                            break;
                                        case 'vendor_bill_date':
                                        case 'vendor_bill_date_of_deduction':
                                            $columnClass = 'col-date';
                                            break;
                                        case 'taxable_amount':
                                        case 'gst_amount':
                                        case 'total_bill_amount':
                                        case 'vendor_bill_tds':
                                        case 'vendor_bill_amount':
                                            $columnClass = 'col-amount';
                                            break;
                                        case 'rate':
                                            $columnClass = 'col-rate';
                                            break;
                                    }
                                @endphp
                                <th class="{{ $columnClass }}">
                                    @switch($header)
                                        @case('vendor_name')
                                            Vendor Name
                                            @break
                                        @case('vendor_address')
                                            Vendor Address
                                            @break
                                        @case('vendor_bill_num')
                                            Bill Number
                                            @break
                                        @case('vendor_pan_num')
                                            PAN Number
                                            @break
                                        @case('vendor_is_company')
                                            Company Type
                                            @break
                                        @case('section')
                                            Section
                                            @break
                                        @case('vendor_bill_date')
                                            Bill Date
                                            @break
                                        @case('vendor_bill_date_of_deduction')
                                            Date of Deduction
                                            @break
                                        @case('taxable_amount')
                                            Taxable Amount
                                            @break
                                        @case('gst_amount')
                                            GST Amount
                                            @break
                                        @case('total_bill_amount')
                                            Total Bill Amount
                                            @break
                                        @case('rate')
                                            Rate
                                            @break
                                        @case('vendor_bill_tds')
                                            TDS Amount
                                            @break
                                        @case('vendor_bill_amount')
                                            Net Amount
                                            @break
                                        @default
                                            {{ ucwords(str_replace('_', ' ', $header)) }}
                                    @endswitch
                                </th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                @if($header !== 'id')
                                    @php
                                        $cellClass = '';
                                        switch($header) {
                                            case 'vendor_name':
                                                $cellClass = 'col-vendor-name';
                                                break;
                                            case 'vendor_address':
                                                $cellClass = 'col-vendor-address';
                                                break;
                                            case 'vendor_bill_num':
                                                $cellClass = 'col-bill-number';
                                                break;
                                            case 'vendor_pan_num':
                                                $cellClass = 'col-pan-number';
                                                break;
                                            case 'vendor_is_company':
                                                $cellClass = 'col-company-type';
                                                break;
                                            case 'section':
                                                $cellClass = 'col-section';
                                                break;
                                            case 'vendor_bill_date':
                                            case 'vendor_bill_date_of_deduction':
                                                $cellClass = 'col-date';
                                                break;
                                            case 'taxable_amount':
                                            case 'gst_amount':
                                            case 'total_bill_amount':
                                            case 'vendor_bill_tds':
                                            case 'vendor_bill_amount':
                                                $cellClass = 'col-amount';
                                                break;
                                            case 'rate':
                                                $cellClass = 'col-rate';
                                                break;
                                        }
                                    @endphp
                                    <td class="{{ $cellClass }}">
                                        @if(in_array($header, ['taxable_amount', 'gst_amount', 'total_bill_amount', 'vendor_bill_tds', 'vendor_bill_amount']) && is_numeric($row[$header]))
                                            {{ number_format($row[$header], 2) }}
                                        @elseif($header === 'vendor_is_company')
                                            {{ $row[$header] == 1 ? 'Company' : 'Individual' }}
                                        @elseif(in_array($header, ['vendor_bill_date', 'vendor_bill_date_of_deduction']) && $row[$header])
                                            {{ date('d/m/Y', strtotime($row[$header])) }}
                                        @else
                                            {{ $row[$header] ?? '' }}
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                    
                    @if(!empty($totalsData))
                        @php
                            $totalRow = reset($totalsData);
                        @endphp
                        <tr class="total-row">
                            @foreach($headers as $header)
                                @if($header !== 'id')
                                    <td class="{{ in_array($header, ['taxable_amount', 'gst_amount', 'total_bill_amount', 'vendor_bill_tds', 'vendor_bill_amount']) ? 'text-right' : 'text-center' }}">
                                        @if($header === 'vendor_name')
                                            <strong>TOTAL</strong>
                                        @elseif($header === 'vendor_bill_tds' && isset($totalRow['vendor_bill_tds']))
                                            <strong>{{ number_format($totalRow['vendor_bill_tds'], 2) }}</strong>
                                        @elseif($header === 'vendor_bill_amount' && isset($totalRow['vendor_bill_amount']))
                                            <strong>{{ number_format($totalRow['vendor_bill_amount'], 2) }}</strong>
                                        @else

                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endif
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        @if(isset($summaryData['vendor_bill_tds']))
                        <th class="text-center">Total TDS Amount</th>
                        @endif
                        @if(isset($summaryData['vendor_bill_amount']))
                        <th class="text-center">Total Net Amount</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['vendor_bill_tds']))
                        <td class="text-center">{{ number_format($summaryData['vendor_bill_tds'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['vendor_bill_amount']))
                        <td class="text-center">{{ number_format($summaryData['vendor_bill_amount'], 2) }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
