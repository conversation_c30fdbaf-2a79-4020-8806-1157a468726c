<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Society Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #ddd; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table thead { background-color: #f0f0f0; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .section-header { font-weight: bold; text-align: left; background: #f9f9f9; font-size: 1.1em; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; }
        .report-info { text-align: center; margin-bottom: 20px; }
        .report-info div { margin: 5px 0; }
        .summary-section { margin-top: 30px; }
        .summary-title { font-weight: bold; font-size: 1.3em; margin-bottom: 15px; }
        .summary-table { background-color: #f9f9f9; }
        .summary-table th, .summary-table td { background-color: #f9f9f9; font-weight: bold; }
        .total-row { background-color: #e9e9e9; font-weight: bold; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'TDS Payable' ?? 'Society Report' }}</p>
    </div>
    
    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>
    
    @if(isset($report_title))
    <div class="sub-title">{{ $report_title }}</div>
    @endif
    
    @if(isset($date_range))
    <div class="text-center">
        <strong>Date: {{ $date_range }}</strong>
    </div>
    @endif
    
    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @if(isset($data) && is_array($data) && count($data) > 0)
            @php
                $reportData = $data[0] ?? [];
                $totalsData = $data[1] ?? [];
                
                // Get dynamic headers from first row of data
                $headers = [];
                if (!empty($reportData)) {
                    $firstRow = reset($reportData);
                    if (is_array($firstRow)) {
                        $headers = array_keys($firstRow);
                    }
                }
            @endphp
            
            @if(!empty($headers) && !empty($reportData))
            <table>
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            @if($header !== 'id')
                                <th>
                                    @switch($header)
                                        @case('vendor_name')
                                            Vendor Name
                                            @break
                                        @case('vendor_address')
                                            Vendor Address
                                            @break
                                        @case('vendor_bill_num')
                                            Bill Number
                                            @break
                                        @case('vendor_pan_num')
                                            PAN Number
                                            @break
                                        @case('vendor_is_company')
                                            Company Type
                                            @break
                                        @case('section')
                                            Section
                                            @break
                                        @case('vendor_bill_date')
                                            Bill Date
                                            @break
                                        @case('vendor_bill_date_of_deduction')
                                            Date of Deduction
                                            @break
                                        @case('taxable_amount')
                                            Taxable Amount
                                            @break
                                        @case('gst_amount')
                                            GST Amount
                                            @break
                                        @case('total_bill_amount')
                                            Total Bill Amount
                                            @break
                                        @case('rate')
                                            Rate
                                            @break
                                        @case('vendor_bill_tds')
                                            TDS Amount
                                            @break
                                        @case('vendor_bill_amount')
                                            Net Amount
                                            @break
                                        @default
                                            {{ ucwords(str_replace('_', ' ', $header)) }}
                                    @endswitch
                                </th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            @foreach($headers as $header)
                                @if($header !== 'id')
                                    <td class="{{ in_array($header, ['taxable_amount', 'gst_amount', 'total_bill_amount', 'vendor_bill_tds', 'vendor_bill_amount']) ? 'text-right' : '' }}">
                                        @if(in_array($header, ['taxable_amount', 'gst_amount', 'total_bill_amount', 'vendor_bill_tds', 'vendor_bill_amount']) && is_numeric($row[$header]))
                                            {{ number_format($row[$header], 2) }}
                                        @elseif($header === 'vendor_is_company')
                                            {{ $row[$header] == 1 ? 'Company' : 'Individual' }}
                                        @elseif(in_array($header, ['vendor_bill_date', 'vendor_bill_date_of_deduction']) && $row[$header])
                                            {{ date('d/m/Y', strtotime($row[$header])) }}
                                        @else
                                            {{ $row[$header] ?? '' }}
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                    
                    @if(!empty($totalsData))
                        @php
                            $totalRow = reset($totalsData);
                        @endphp
                        <tr class="total-row">
                            @foreach($headers as $header)
                                @if($header !== 'id')
                                    <td class="{{ in_array($header, ['taxable_amount', 'gst_amount', 'total_bill_amount', 'vendor_bill_tds', 'vendor_bill_amount']) ? 'text-right' : 'text-center' }}">
                                        @if($header === 'vendor_name')
                                            <strong>TOTAL</strong>
                                        @elseif($header === 'vendor_bill_tds' && isset($totalRow['vendor_bill_tds']))
                                            <strong>{{ number_format($totalRow['vendor_bill_tds'], 2) }}</strong>
                                        @elseif($header === 'vendor_bill_amount' && isset($totalRow['vendor_bill_amount']))
                                            <strong>{{ number_format($totalRow['vendor_bill_amount'], 2) }}</strong>
                                        @else

                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endif
                </tbody>
            </table>
            @endif
        @endif
    </div>
    
    @if(isset($data) && is_array($data) && count($data) > 1 && !empty($data[1]))
        @php
            $summaryData = reset($data[1]);
        @endphp
        <div class="summary-section">
            <div class="summary-title">Summary</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        @if(isset($summaryData['vendor_bill_tds']))
                        <th class="text-center">Total TDS Amount</th>
                        @endif
                        @if(isset($summaryData['vendor_bill_amount']))
                        <th class="text-center">Total Net Amount</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @if(isset($summaryData['vendor_bill_tds']))
                        <td class="text-center">{{ number_format($summaryData['vendor_bill_tds'], 2) }}</td>
                        @endif
                        @if(isset($summaryData['vendor_bill_amount']))
                        <td class="text-center">{{ number_format($summaryData['vendor_bill_amount'], 2) }}</td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    @endif
    
    @if(isset($report_date))
    <div class="report-date">
        Report date: {{ $report_date }}
    </div>
    @endif
    
    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
