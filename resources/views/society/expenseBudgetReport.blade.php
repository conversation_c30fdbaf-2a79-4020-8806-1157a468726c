<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Expense Budget Report' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .expense-account-col { width: 20%; text-align: left; }
        .expense-ledger-col { width: 25%; text-align: left; }
        .budget-col { width: 18%; text-align: right; }
        .actual-expense-col { width: 18%; text-align: right; }
        .variance-col { width: 19%; text-align: right; }
        .no-records { text-align: center; padding: 20px; font-style: italic; color: #666; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Expense Budget Report' ?? 'Society Report' }}</p>
    </div>


    

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                <tr>
                    <th colspan="5" style="text-align:center;padding:10px">
                        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
                        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
                        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
                    </th>
                </tr>
                <tr>
                    <th class="small-font expense-account-col">Expense Account</th>
                        <th class="small-font expense-ledger-col">Expense Ledger</th>
                        <th class="small-font budget-col">Budget</th>
                        <th class="small-font actual-expense-col">Actual Expense</th>
                        <th class="small-font variance-col">Variance</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        <tr>
                            <td class="small-font expense-account-col">{{ $row['expense_account'] ?? '' }}</td>
                            <td class="small-font expense-ledger-col">{{ $row['expense_ledger'] ?? '' }}</td>
                            <td class="small-font budget-col">{{ number_format($row['budget'] ?? 0, 2) }}</td>
                            <td class="small-font actual-expense-col">{{ $row['actual_expense'] ?? '' }}</td>
                            <td class="small-font variance-col">{{ $row['variance'] ?? '' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="no-records">
                <p>No expense budget data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
