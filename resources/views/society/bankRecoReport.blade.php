<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $society_name ?? 'Bank Reco Statement' }}</title>
    <link rel="shortcut icon" href="https://stgsociety.chsone.in/images/favicon.png" />
    <style>
        html { font-size: 62.5%; margin: 0; }
        body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; margin: 0; color: #333; background: #fff; }
        table { background: white; border: solid 1px #000; width: 100%; margin: 20px 0; border-collapse: collapse; }
        table th, table td { padding: 6px; border: 1px solid #000; text-align: left; font-size: 10px; }
        table thead { background-color: #f0f0f0; }
        table th { font-weight: bold; text-align: center; }
        .printOption { background: #001941; height: 40px; padding: 10px; color: #fff; }
        .footer { background: #001941; padding: 10px; color: #fff; text-align: right; }
        .main-title { text-align: center; font-size: 1.6em; font-weight: bold; margin-top: 20px; }
        .sub-title { text-align: center; font-size: 1.2em; margin-bottom: 20px; color: #666; }
        .filter-info { text-align: center; font-size: 1.0em; margin-bottom: 20px; color: #333; }
        .report-date { text-align: left; margin-top: 20px; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .small-font { font-size: 9px; }
        .sr-col { width: 5%; text-align: center; }
        .status-col { width: 10%; text-align: center; }
        .from-col { width: 12%; text-align: left; }
        .date-col { width: 8%; text-align: center; }
        .particulars-col { width: 20%; text-align: left; }
        .payment-mode-col { width: 8%; text-align: center; }
        .payment-ref-col { width: 8%; text-align: center; }
        .bank-date-col { width: 8%; text-align: center; }
        .deposit-col { width: 8%; text-align: right; }
        .withdrawal-col { width: 8%; text-align: right; }
        .type-col { width: 8%; text-align: center; }
        .total-row { background-color: #f0f0f0; font-weight: bold; }
    </style>
</head>
<body>
    <div class="printOption">
        <p>{{ 'Bank Reco Statement' ?? 'Society Report' }}</p>
    </div>

    <div style="width:100%;text-align:center;padding:10px;">
        <h2 style="margin-bottom:0;margin-top:5px">{{$company_details['society_name'] ?? 'River Walk CHS'}}</h2>
        <h3 style="margin-bottom:0;margin-top:5px">Reg No: {{$company_details['society_reg_num'] ?? 'AKKL123456789'}} | GSTIN/UIN: {{$company_details['gstin'] ?? '27TGDCBA1234G'}}</h3>
        <p style="margin-bottom:0;margin-top:5px">{{$company_details['society_address'] ?? 'Society Address'}}</p>
    </div>

    <div class="sub-title">Bank Reco Statement</div>

    <div class="tableDiv" style="width: 98%; margin: 0 auto;">
        @php
            // Initialize variables with safe defaults
            $reportData = [];
            $totalDeposit = 0;
            $totalWithdrawal = 0;

            // Check if data exists and is properly structured
            if (isset($data) && is_array($data)) {
                $reportData = $data;
            }
        @endphp

        @if(!empty($reportData))
            
            <table>
                <thead>
                    <tr>
                        <th class="small-font sr-col">Sr. No</th>
                        <th class="small-font status-col">Reconciliation Status</th>
                        <th class="small-font from-col">From Account</th>
                        <th class="small-font date-col">Date</th>
                        <th class="small-font particulars-col">Particulars/Narration</th>
                        <th class="small-font payment-mode-col">Payment Mode</th>
                        <th class="small-font payment-ref-col">Payment Reference</th>
                        <th class="small-font bank-date-col">Bank Date</th>
                        <th class="small-font deposit-col">Deposit</th>
                        <th class="small-font withdrawal-col">Withdrawal</th>
                        <th class="small-font type-col">Type(Payment / Receipt)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($reportData as $row)
                        @if(isset($row['sr_no']) && $row['sr_no'] !== '')
                            {{-- Regular data row --}}
                            <tr>
                                <td class="small-font sr-col">{{ $row['sr_no'] ?? '' }}</td>
                                <td class="small-font status-col">{{ $row['reconciliation_status'] ?? '' }}</td>
                                <td class="small-font from-col">{{ $row['from_account'] ?? '' }}</td>
                                <td class="small-font date-col">{{ $row['date'] ?? '' }}</td>
                                <td class="small-font particulars-col">{{ $row['particulars_narration'] ?? '' }}</td>
                                <td class="small-font payment-mode-col">{{ ucfirst($row['payment_mode'] ?? '') }}</td>
                                <td class="small-font payment-ref-col">{{ $row['payment_reference'] ?? '' }}</td>
                                <td class="small-font bank-date-col">{{ $row['bank_date'] ?? '' }}</td>
                                <td class="small-font deposit-col">{{ number_format($row['deposit'] ?? 0, 2) }}</td>
                                <td class="small-font withdrawal-col">{{ number_format($row['withdrawal'] ?? 0, 2) }}</td>
                                <td class="small-font type-col">{{ $row['type'] ?? '' }}</td>
                            </tr>
                        @elseif(isset($row['id']) && $row['id'] === 'Total')
                            {{-- Total row --}}
                            <tr class="total-row">
                                <td class="small-font" colspan="8"><strong>Total</strong></td>
                                <td class="small-font deposit-col"><strong>{{ number_format($row['deposit'] ?? 0, 2) }}</strong></td>
                                <td class="small-font withdrawal-col"><strong>{{ number_format($row['withdrawal'] ?? 0, 2) }}</strong></td>
                                <td class="small-font type-col"></td>
                            </tr>
                        @elseif(isset($row['id']) && $row['id'] === '')
                            {{-- Empty separator row --}}
                            <tr>
                                <td class="small-font" colspan="11">&nbsp;</td>
                            </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>

        @else
            <div class="text-center" style="padding: 20px;">
                <p>No bank reconciliation data available for the selected criteria.</p>
            </div>
        @endif
    </div>

    <div class="report-date">
        Report date: {{ $report_date ?? date('d/m/Y') }}
    </div>

    <div class="footer">
        <span>Powered By:</span> <img src="https://stgsociety.chsone.in/images/logo.png" alt="CHSONE" style="vertical-align: middle;">
    </div>
</body>
</html>
